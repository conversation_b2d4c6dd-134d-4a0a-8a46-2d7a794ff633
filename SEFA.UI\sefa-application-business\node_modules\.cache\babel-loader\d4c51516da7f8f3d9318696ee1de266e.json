{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue", "mtime": 1749634431430}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICdAL3ZpZXdzL0ludmVudG9yeS9teXN0eWxlLnNjc3MnOwppbXBvcnQgeyBzcGxpdE1hdGVyaWFsIH0gZnJvbSAnQC9hcGkvbWF0ZXJpYWxNYW5hZ2VtZW50L2xpbmVMaWJyYXJ5TWFuYWdlbWVudC5qcyc7CmltcG9ydCB7IE1lc3NhZ2UgfSBmcm9tICdlbGVtZW50LXVpJzsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzc2NjOiAnJywKICAgICAgc3BsaXRRdWFudGl0eTogJycsCiAgICAgIHNzY2NGbGFnOiBmYWxzZSwKICAgICAgc3BsaXRWaXNpYmxlOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRNYXRlcmlhbDogbnVsbCwKICAgICAgZGV0YWlsb2JqOiB7fQogICAgfTsKICB9LAoKICBjb21wdXRlZDogewogICAgY2FuU3BsaXQoKSB7CiAgICAgIHJldHVybiB0aGlzLnNwbGl0VmlzaWJsZSAmJiB0aGlzLnNzY2NGbGFnICYmIHRoaXMuc3NjYyAhPT0gJycgJiYgdGhpcy5zcGxpdFF1YW50aXR5ICE9PSAnJyAmJiBOdW1iZXIodGhpcy5zcGxpdFF1YW50aXR5KSA+IDAgJiYgdGhpcy5zZWxlY3RlZE1hdGVyaWFsICYmIE51bWJlcih0aGlzLnNwbGl0UXVhbnRpdHkpIDwgTnVtYmVyKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbC5JblF1YW50aXR5KTsKICAgIH0KCiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZGV0YWlsb2JqID0gSlNPTi5wYXJzZSh0aGlzLiRyb3V0ZS5xdWVyeS5xdWVyeSk7CiAgfSwKCiAgbWV0aG9kczogewogICAgdG9nZ2xlU3BsaXRWaXNpYmlsaXR5KCkgewogICAgICB0aGlzLnNwbGl0VmlzaWJsZSA9ICF0aGlzLnNwbGl0VmlzaWJsZTsKCiAgICAgIGlmICghdGhpcy5zcGxpdFZpc2libGUpIHsKICAgICAgICB0aGlzLnJlc2V0U3BsaXRGb3JtKCk7CiAgICAgIH0KICAgIH0sCgogICAgcmVzZXRTcGxpdEZvcm0oKSB7CiAgICAgIHRoaXMuc3NjYyA9ICcnOwogICAgICB0aGlzLnNwbGl0UXVhbnRpdHkgPSAnJzsKICAgICAgdGhpcy5zc2NjRmxhZyA9IGZhbHNlOwogICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwgPSBudWxsOwogICAgfSwKCiAgICBnZXRSb3dCeVNTQ0MoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ2dldFJvd0J5U3NjYycsIHRoaXMuc3NjYyk7CiAgICB9LAoKICAgIC8vIOS7jueItue7hOS7tuaOpeaUtumAieS4reeahOeJqeaWmeS/oeaBrwogICAgc2V0U2VsZWN0ZWRNYXRlcmlhbChtYXRlcmlhbCkgewogICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwgPSBtYXRlcmlhbDsKICAgICAgdGhpcy5zc2NjRmxhZyA9IHRydWU7CiAgICB9LAoKICAgIGFzeW5jIHBlcmZvcm1TcGxpdCgpIHsKICAgICAgaWYgKCF0aGlzLmNhblNwbGl0KSB7CiAgICAgICAgTWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiB0aGlzLiR0KCdNYXRlcmlhbFByZXBhcmF0aW9uQnVpbGQuU3BsaXRDb25kaXRpb25Ob3RNZXQnKSwKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBzcGxpdERhdGEgPSB7CiAgICAgICAgICBtYXRlcmlhbElkOiB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwuTWF0ZXJpYWxJZCwKICAgICAgICAgIHNzY2M6IHRoaXMuc3NjYywKICAgICAgICAgIG9yaWdpbmFsUXVhbnRpdHk6IHRoaXMuc2VsZWN0ZWRNYXRlcmlhbC5JblF1YW50aXR5LAogICAgICAgICAgc3BsaXRRdWFudGl0eTogTnVtYmVyKHRoaXMuc3BsaXRRdWFudGl0eSksCiAgICAgICAgICBiYXRjaE51bWJlcjogdGhpcy5zZWxlY3RlZE1hdGVyaWFsLkxCYXRjaCwKICAgICAgICAgIGVxdWlwbWVudElkOiB3aW5kb3cuc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgncm9vbScpLAogICAgICAgICAgdW5pdElkOiB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwuVW5pdElkCiAgICAgICAgfTsKICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzcGxpdE1hdGVyaWFsKHNwbGl0RGF0YSk7CiAgICAgICAgTWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiByZXN1bHQubXNnIHx8IHRoaXMuJHQoJ01hdGVyaWFsUHJlcGFyYXRpb25CdWlsZC5TcGxpdFN1Y2Nlc3MnKSwKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgIH0pOyAvLyDph43nva7ooajljZUKCiAgICAgICAgdGhpcy5yZXNldFNwbGl0Rm9ybSgpOyAvLyDpgJrnn6XniLbnu4Tku7bliLfmlrDmlbDmja4KCiAgICAgICAgdGhpcy4kZW1pdCgnZ2V0UmVmcmVzaCcpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIE1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB8fCB0aGlzLiR0KCdNYXRlcmlhbFByZXBhcmF0aW9uQnVpbGQuU3BsaXRGYWlsZWQnKSwKICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICB9KTsKICAgICAgfQogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AAgEA;AACA;AACA;AAEA;EACAA;IACA;MACAC,QADA;MAEAC,iBAFA;MAGAC,eAHA;MAIAC,mBAJA;MAKAC,sBALA;MAMAC;IANA;EAQA,CAVA;;EAWAC;IACAC;MACA,4BACA,aADA,IAEA,gBAFA,IAGA,yBAHA,IAIAC,8BAJA,IAKA,qBALA,IAMAA,qEANA;IAOA;;EATA,CAXA;;EAsBAC;IACA;EACA,CAxBA;;EAyBAC;IACAC;MACA;;MACA;QACA;MACA;IACA,CANA;;IAQAC;MACA;MACA;MACA;MACA;IACA,CAbA;;IAeAC;MACA;IACA,CAjBA;;IAmBA;IACAC;MACA;MACA;IACA,CAvBA;;IAyBA;MACA;QACAC;UACAC,iEADA;UAEAC;QAFA;QAIA;MACA;;MAEA;QACA;UACAC,4CADA;UAEAlB,eAFA;UAGAmB,kDAHA;UAIAlB,yCAJA;UAKAmB,yCALA;UAMAC,kDANA;UAOAC;QAPA;QAUA;QAEAP;UACAC,uEADA;UAEAC;QAFA,GAbA,CAkBA;;QACA,sBAnBA,CAqBA;;QACA;MAEA,CAxBA,CAwBA;QACAF;UACAC,yEADA;UAEAC;QAFA;MAIA;IACA;;EAhEA;AAzBA", "names": ["data", "sscc", "splitQuantity", "ssccFlag", "splitVisible", "selectedMaterial", "<PERSON><PERSON><PERSON>", "computed", "canSplit", "Number", "mounted", "methods", "toggleSplitVisibility", "resetSplitForm", "getRowBySSCC", "setSelectedMaterial", "Message", "message", "type", "materialId", "originalQuantity", "batchNumber", "equipmentId", "unitId"], "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sources": ["MaterialSplit.vue"], "sourcesContent": ["<template>\n    <div class=\"usemystyle MaterialSplit\">\n        <div class=\"tabinputbox\">\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    @change=\"getRowBySSCC\" \n                    ref=\"autoFocus\" \n                    :placeholder=\"$t('Consume.SSCC')\" \n                    v-model=\"sscc\"\n                    :disabled=\"!splitVisible\">\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    :placeholder=\"$t('MaterialPreparationBuild.SplitQuantity')\" \n                    v-model=\"splitQuantity\"\n                    :disabled=\"!splitVisible || !ssccFlag\">\n                    <template slot=\"append\">{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}</template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <div class=\"tabbtnsinglebox\">\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        :disabled=\"!canSplit\" \n                        size=\"small\" \n                        icon=\"el-icon-scissors\" \n                        class=\"tablebtn\" \n                        @click=\"performSplit()\">\n                        {{ this.$t('MaterialPreparationBuild.Split') }}\n                    </el-button>\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        size=\"small\" \n                        icon=\"el-icon-view\" \n                        @click=\"toggleSplitVisibility()\">\n                        {{ splitVisible ? $t('MaterialPreparationBuild.HideSplit') : $t('MaterialPreparationBuild.ShowSplit') }}\n                    </el-button>\n                </div>\n            </div>\n        </div>\n        \n        <!-- 分包信息显示 -->\n        <div v-if=\"splitVisible && selectedMaterial\" class=\"split-info-box\">\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.SelectedMaterial') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.MaterialCode }} - {{ selectedMaterial.MaterialName }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.AvailableQuantity') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.InQuantity }}{{ selectedMaterial.MaterialUnit1 }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.BatchNumber') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.LBatch }}</span>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport { splitMaterial } from '@/api/materialManagement/lineLibraryManagement.js';\nimport { Message } from 'element-ui';\n\nexport default {\n    data() {\n        return {\n            sscc: '',\n            splitQuantity: '',\n            ssccFlag: false,\n            splitVisible: false,\n            selectedMaterial: null,\n            detailobj: {}\n        };\n    },\n    computed: {\n        canSplit() {\n            return this.splitVisible && \n                   this.ssccFlag && \n                   this.sscc !== '' && \n                   this.splitQuantity !== '' && \n                   Number(this.splitQuantity) > 0 &&\n                   this.selectedMaterial &&\n                   Number(this.splitQuantity) < Number(this.selectedMaterial.InQuantity);\n        }\n    },\n    mounted() {\n        this.detailobj = JSON.parse(this.$route.query.query);\n    },\n    methods: {\n        toggleSplitVisibility() {\n            this.splitVisible = !this.splitVisible;\n            if (!this.splitVisible) {\n                this.resetSplitForm();\n            }\n        },\n        \n        resetSplitForm() {\n            this.sscc = '';\n            this.splitQuantity = '';\n            this.ssccFlag = false;\n            this.selectedMaterial = null;\n        },\n        \n        getRowBySSCC() {\n            this.$emit('getRowBySscc', this.sscc);\n        },\n        \n        // 从父组件接收选中的物料信息\n        setSelectedMaterial(material) {\n            this.selectedMaterial = material;\n            this.ssccFlag = true;\n        },\n        \n        async performSplit() {\n            if (!this.canSplit) {\n                Message({\n                    message: this.$t('MaterialPreparationBuild.SplitConditionNotMet'),\n                    type: 'warning'\n                });\n                return;\n            }\n            \n            try {\n                const splitData = {\n                    materialId: this.selectedMaterial.MaterialId,\n                    sscc: this.sscc,\n                    originalQuantity: this.selectedMaterial.InQuantity,\n                    splitQuantity: Number(this.splitQuantity),\n                    batchNumber: this.selectedMaterial.LBatch,\n                    equipmentId: window.sessionStorage.getItem('room'),\n                    unitId: this.selectedMaterial.UnitId\n                };\n                \n                const result = await splitMaterial(splitData);\n                \n                Message({\n                    message: result.msg || this.$t('MaterialPreparationBuild.SplitSuccess'),\n                    type: 'success'\n                });\n                \n                // 重置表单\n                this.resetSplitForm();\n                \n                // 通知父组件刷新数据\n                this.$emit('getRefresh');\n                \n            } catch (error) {\n                Message({\n                    message: error.message || this.$t('MaterialPreparationBuild.SplitFailed'),\n                    type: 'error'\n                });\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.MaterialSplit {\n    .split-info-box {\n        margin-top: 10px;\n        padding: 10px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border: 1px solid #e4e7ed;\n        \n        .info-item {\n            display: flex;\n            margin-bottom: 5px;\n            \n            .info-label {\n                font-weight: 600;\n                color: #606266;\n                min-width: 120px;\n            }\n            \n            .info-value {\n                color: #303133;\n            }\n        }\n    }\n}\n</style>\n"]}]}
{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749634526901}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <!-- 统一备料统计信息区域 -->\r\n            <div class=\"searchbox\">\r\n                <!-- 第一行：整数袋和零数袋统计 -->\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: preparationStats.fullBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ preparationStats.fullBagCompleted }}/{{ preparationStats.fullBagTotal }}袋\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.partialBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.PartialBags') }}：{{ preparationStats.partialBagCompleted.toFixed(3) }}/{{ preparationStats.partialBagTotal.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第二行：总体备料统计 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalRequired') }}：{{ preparationStats.totalRequired.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalCompleted') }}：{{ preparationStats.totalCompleted.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第三行：理论值、最小值、最大值 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Theoretical') }}：{{ preparationStats.theoreticalValue.toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox adaptive-inventory-container\" :style=\"{\r\n                '--table-height': tableHeight + 'px',\r\n                '--dynamic-table-height': tableHeight + 'px'\r\n            }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                ({{ tableList.length }}行 | {{ tableHeight }}px | {{ useViewportHeight ? '视口模式' : '行数模式' }})\r\n                            </span> -->\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px; align-items: center;\">\r\n                            <!-- 高度模式切换按钮 -->\r\n                            <el-button\r\n                                size=\"mini\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\"\r\n                                @click=\"toggleHeightMode()\"\r\n                                :title=\"useViewportHeight ? '当前：视口模式，点击切换到行数模式' : '当前：行数模式，点击切换到视口模式'\"\r\n                                style=\"margin-right: 5px;\">\r\n                                {{ useViewportHeight ? '视口' : '行数' }}\r\n                            </el-button>\r\n\r\n                            <!-- 打印按钮 -->\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 自适应高度表格 -->\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :class=\"['adaptive-table', `height-mode-${useViewportHeight ? 'viewport' : 'rows'}`]\"\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px',\r\n                        maxHeight: tableHeight + 'px',\r\n                        minHeight: tableHeight + 'px'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableRenderKey\"\r\n                    size=\"small\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.MaterialSplit')\" name=\"MaterialSplit\">\r\n                        <MaterialSplit ref=\"MaterialSplit\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></MaterialSplit>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets'),\r\n        MaterialSplit: () => import('./components/MaterialSplit')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n\r\n            // 🎯 备料统计数据\r\n            preparationStats: {\r\n                fullBagCompleted: 0,        // 整数袋已完成数量\r\n                fullBagTotal: 0,            // 整数袋理论数量\r\n                fullBagComplete: false,     // 整数袋是否完成\r\n                partialBagCompleted: 0,     // 零数袋已完成重量\r\n                partialBagTotal: 0,         // 零数袋理论重量\r\n                partialBagComplete: false,  // 零数袋是否完成\r\n                totalRequired: 0,           // 总需求重量\r\n                totalCompleted: 0,          // 总已完成重量\r\n                totalComplete: false,       // 总体是否完成\r\n                theoreticalValue: 0         // 理论值\r\n            },\r\n\r\n            // 🎯 表格自适应高度配置\r\n            minTableHeight: 180,        // 最小高度（表头+至少2行）\r\n            maxTableHeight: 500,        // 最大高度\r\n            baseRowHeight: 42,          // 基础行高\r\n            actualRowHeight: 42,        // 实际检测到的行高\r\n            headerHeight: 40,           // 表头高度\r\n            windowHeight: window.innerHeight,\r\n            useViewportHeight: false,   // 默认使用行数模式\r\n            tableHeight: 220,           // 当前表格高度\r\n            tableRenderKey: 0,          // 强制重新渲染\r\n            isCalculating: false,       // 防止重复计算\r\n            resizeTimer: null           // 防抖定时器\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 🎯 初始化表格自适应高度功能\r\n        this.initAdaptiveHeight();\r\n\r\n        // 🎯 初始化统计数据\r\n        this.$nextTick(() => {\r\n            this.calculatePreparationStats();\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n        if (this.resizeTimer) {\r\n            clearTimeout(this.resizeTimer);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n\r\n        // 🎯 表格自适应高度核心方法\r\n        initAdaptiveHeight() {\r\n            console.log('=== 初始化表格自适应高度 ===');\r\n\r\n            // 添加窗口大小变化监听器\r\n            this.handleResize = () => {\r\n                if (this.resizeTimer) {\r\n                    clearTimeout(this.resizeTimer);\r\n                }\r\n                this.resizeTimer = setTimeout(() => {\r\n                    this.windowHeight = window.innerHeight;\r\n                    if (this.useViewportHeight) {\r\n                        this.calculateTableHeight();\r\n                    }\r\n                }, 300);\r\n            };\r\n            window.addEventListener('resize', this.handleResize);\r\n\r\n            // 初始化表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n\r\n        // 计算表格高度\r\n        calculateTableHeight() {\r\n            if (this.isCalculating) return;\r\n            this.isCalculating = true;\r\n\r\n            console.log('=== 计算表格高度 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('数据行数:', this.tableList ? this.tableList.length : 0);\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('无数据，使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 视口模式：基于窗口高度的25%\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 行数模式：基于数据行数智能计算\r\n                newHeight = this.calculateRowBasedHeight();\r\n            }\r\n\r\n            // 更新高度\r\n            if (Math.abs(this.tableHeight - newHeight) > 2) {\r\n                this.tableHeight = newHeight;\r\n                this.tableRenderKey++;\r\n                console.log('高度已更新:', this.tableHeight, 'px');\r\n\r\n                this.$nextTick(() => {\r\n                    this.applyTableHeight();\r\n                });\r\n            }\r\n\r\n            this.isCalculating = false;\r\n        },\r\n\r\n        // 基于行数计算高度\r\n        calculateRowBasedHeight() {\r\n            const padding = 8;\r\n            const borderHeight = 2;\r\n\r\n            // 检测实际行高\r\n            this.detectActualRowHeight();\r\n\r\n            // 计算总高度：表头 + 数据行 + 边距\r\n            const totalHeight = this.headerHeight +\r\n                               (this.tableList.length * this.actualRowHeight) +\r\n                               padding +\r\n                               borderHeight;\r\n\r\n            const finalHeight = Math.min(Math.max(totalHeight, this.minTableHeight), this.maxTableHeight);\r\n\r\n            console.log('行数模式计算:', {\r\n                headerHeight: this.headerHeight,\r\n                dataRows: this.tableList.length,\r\n                actualRowHeight: this.actualRowHeight,\r\n                totalHeight,\r\n                finalHeight\r\n            });\r\n\r\n            return finalHeight;\r\n        },\r\n\r\n        // 检测实际行高\r\n        detectActualRowHeight() {\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                this.$nextTick(() => {\r\n                    const tableEl = this.$refs.TopTabel.$el;\r\n                    const firstRow = tableEl.querySelector('.el-table__body tr');\r\n                    if (firstRow) {\r\n                        const detectedHeight = firstRow.offsetHeight;\r\n                        if (detectedHeight > 0 && Math.abs(detectedHeight - this.actualRowHeight) > 2) {\r\n                            console.log('检测到新的行高:', detectedHeight, '(原:', this.actualRowHeight, ')');\r\n                            this.actualRowHeight = detectedHeight;\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        },\r\n\r\n        // 应用表格高度到DOM\r\n        applyTableHeight() {\r\n            if (this.$refs.TopTabel) {\r\n                const tableComponent = this.$refs.TopTabel;\r\n                const tableEl = tableComponent.$el;\r\n\r\n                // 设置表格高度\r\n                tableEl.style.height = this.tableHeight + 'px';\r\n                tableEl.style.maxHeight = this.tableHeight + 'px';\r\n                tableEl.style.minHeight = this.tableHeight + 'px';\r\n\r\n                // 设置表格内容区域高度\r\n                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                if (bodyWrapper) {\r\n                    bodyWrapper.style.maxHeight = (this.tableHeight - this.headerHeight) + 'px';\r\n                }\r\n\r\n                // 调用Element UI的布局方法\r\n                if (tableComponent.doLayout) {\r\n                    tableComponent.doLayout();\r\n                }\r\n\r\n                console.log('DOM高度已应用:', this.tableHeight + 'px');\r\n            }\r\n        },\r\n\r\n        // 切换高度模式\r\n        toggleHeightMode() {\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n            console.log('切换到:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n\r\n            this.calculateTableHeight();\r\n        },\r\n\r\n        // 🎯 计算备料统计数据\r\n        calculatePreparationStats() {\r\n            if (!this.detailobj) return;\r\n\r\n            // 计算整数袋统计\r\n            if (this.detailobj.FullPage) {\r\n                const fullPageParts = this.detailobj.FullPage.split('/');\r\n                this.preparationStats.fullBagCompleted = parseInt(fullPageParts[0]) || 0;\r\n                this.preparationStats.fullBagTotal = parseInt(fullPageParts[1]) || 0;\r\n                this.preparationStats.fullBagComplete = this.preparationStats.fullBagCompleted >= this.preparationStats.fullBagTotal;\r\n            }\r\n\r\n            // 计算零数袋统计\r\n            this.preparationStats.partialBagCompleted = Number(this.detailobj.TagpS) || 0;\r\n            this.preparationStats.partialBagTotal = Number(this.detailobj.ParitialPage) || 0;\r\n            this.preparationStats.partialBagComplete = this.preparationStats.partialBagCompleted >= this.preparationStats.partialBagTotal;\r\n\r\n            // 计算总体统计\r\n            this.preparationStats.totalRequired = Number(this.detailobj.MQuantityTotal) || 0;\r\n            this.preparationStats.totalCompleted = Number(this.detailobj.MQuantity) || 0;\r\n            this.preparationStats.totalComplete = this.preparationStats.totalCompleted >= this.preparationStats.totalRequired;\r\n\r\n            // 计算理论值\r\n            this.preparationStats.theoreticalValue = Number(this.detailobj.MQuantityTotal) || 0;\r\n\r\n            console.log('备料统计数据已更新:', this.preparationStats);\r\n        },\r\n\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n\r\n            // 🎯 计算备料统计数据\r\n            this.calculatePreparationStats();\r\n\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n\r\n                // 🎯 如果是分包Tab，将选中的物料信息传递给MaterialSplit组件\r\n                if (this.activeName === 'MaterialSplit' && this.$refs.MaterialSplit) {\r\n                    this.$refs.MaterialSplit.setSelectedMaterial(data);\r\n                }\r\n\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n\r\n            // 🎯 数据刷新后重新计算表格高度和统计数据\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n                this.calculatePreparationStats();\r\n            });\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n\r\n            // 🎯 数据加载完成后重新计算表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 🎯 可用库存表格自适应高度样式\r\n    .adaptive-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 550px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 自适应表格样式\r\n        .adaptive-table {\r\n            transition: height 0.3s ease !important;\r\n            width: 100% !important;\r\n\r\n            // 强制设置表格高度\r\n            &.el-table {\r\n                height: var(--table-height, 220px) !important;\r\n                max-height: var(--table-height, 220px) !important;\r\n                min-height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 表格内容区域自适应\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 220px) - 40px) !important;\r\n                overflow-y: auto !important;\r\n\r\n                // 美化滚动条\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px !important;\r\n                min-height: 40px !important;\r\n                max-height: 40px !important;\r\n            }\r\n\r\n            // 固定列样式\r\n            .el-table__fixed,\r\n            .el-table__fixed-right {\r\n                height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 行数模式特殊样式\r\n            &.height-mode-rows {\r\n                .el-table__body tr {\r\n                    transition: height 0.2s ease;\r\n                }\r\n            }\r\n\r\n            // 视口模式特殊样式\r\n            &.height-mode-viewport {\r\n                .el-table__body-wrapper {\r\n                    overflow-y: auto !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 350px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 280px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}
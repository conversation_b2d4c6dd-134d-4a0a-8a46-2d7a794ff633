{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue?vue&type=style&index=0&id=aa94338e&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue", "mtime": 1749634431430}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5NYXRlcmlhbFNwbGl0IHsKICAgIC5zcGxpdC1pbmZvLWJveCB7CiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKICAgICAgICBwYWRkaW5nOiAxMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmNWY3ZmE7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICAgICAgCiAgICAgICAgLmluZm8taXRlbSB7CiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDVweDsKICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbmZvLWxhYmVsIHsKICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgICAgICAgICAgICBjb2xvcjogIzYwNjI2NjsKICAgICAgICAgICAgICAgIG1pbi13aWR0aDogMTIwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbmZvLXZhbHVlIHsKICAgICAgICAgICAgICAgIGNvbG9yOiAjMzAzMTMzOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["MaterialSplit.vue"], "names": [], "mappings": ";AAmKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "MaterialSplit.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\n    <div class=\"usemystyle MaterialSplit\">\n        <div class=\"tabinputbox\">\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    @change=\"getRowBySSCC\" \n                    ref=\"autoFocus\" \n                    :placeholder=\"$t('Consume.SSCC')\" \n                    v-model=\"sscc\"\n                    :disabled=\"!splitVisible\">\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    :placeholder=\"$t('MaterialPreparationBuild.SplitQuantity')\" \n                    v-model=\"splitQuantity\"\n                    :disabled=\"!splitVisible || !ssccFlag\">\n                    <template slot=\"append\">{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}</template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <div class=\"tabbtnsinglebox\">\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        :disabled=\"!canSplit\" \n                        size=\"small\" \n                        icon=\"el-icon-scissors\" \n                        class=\"tablebtn\" \n                        @click=\"performSplit()\">\n                        {{ this.$t('MaterialPreparationBuild.Split') }}\n                    </el-button>\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        size=\"small\" \n                        icon=\"el-icon-view\" \n                        @click=\"toggleSplitVisibility()\">\n                        {{ splitVisible ? $t('MaterialPreparationBuild.HideSplit') : $t('MaterialPreparationBuild.ShowSplit') }}\n                    </el-button>\n                </div>\n            </div>\n        </div>\n        \n        <!-- 分包信息显示 -->\n        <div v-if=\"splitVisible && selectedMaterial\" class=\"split-info-box\">\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.SelectedMaterial') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.MaterialCode }} - {{ selectedMaterial.MaterialName }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.AvailableQuantity') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.InQuantity }}{{ selectedMaterial.MaterialUnit1 }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.BatchNumber') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.LBatch }}</span>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport { splitMaterial } from '@/api/materialManagement/lineLibraryManagement.js';\nimport { Message } from 'element-ui';\n\nexport default {\n    data() {\n        return {\n            sscc: '',\n            splitQuantity: '',\n            ssccFlag: false,\n            splitVisible: false,\n            selectedMaterial: null,\n            detailobj: {}\n        };\n    },\n    computed: {\n        canSplit() {\n            return this.splitVisible && \n                   this.ssccFlag && \n                   this.sscc !== '' && \n                   this.splitQuantity !== '' && \n                   Number(this.splitQuantity) > 0 &&\n                   this.selectedMaterial &&\n                   Number(this.splitQuantity) < Number(this.selectedMaterial.InQuantity);\n        }\n    },\n    mounted() {\n        this.detailobj = JSON.parse(this.$route.query.query);\n    },\n    methods: {\n        toggleSplitVisibility() {\n            this.splitVisible = !this.splitVisible;\n            if (!this.splitVisible) {\n                this.resetSplitForm();\n            }\n        },\n        \n        resetSplitForm() {\n            this.sscc = '';\n            this.splitQuantity = '';\n            this.ssccFlag = false;\n            this.selectedMaterial = null;\n        },\n        \n        getRowBySSCC() {\n            this.$emit('getRowBySscc', this.sscc);\n        },\n        \n        // 从父组件接收选中的物料信息\n        setSelectedMaterial(material) {\n            this.selectedMaterial = material;\n            this.ssccFlag = true;\n        },\n        \n        async performSplit() {\n            if (!this.canSplit) {\n                Message({\n                    message: this.$t('MaterialPreparationBuild.SplitConditionNotMet'),\n                    type: 'warning'\n                });\n                return;\n            }\n            \n            try {\n                const splitData = {\n                    materialId: this.selectedMaterial.MaterialId,\n                    sscc: this.sscc,\n                    originalQuantity: this.selectedMaterial.InQuantity,\n                    splitQuantity: Number(this.splitQuantity),\n                    batchNumber: this.selectedMaterial.LBatch,\n                    equipmentId: window.sessionStorage.getItem('room'),\n                    unitId: this.selectedMaterial.UnitId\n                };\n                \n                const result = await splitMaterial(splitData);\n                \n                Message({\n                    message: result.msg || this.$t('MaterialPreparationBuild.SplitSuccess'),\n                    type: 'success'\n                });\n                \n                // 重置表单\n                this.resetSplitForm();\n                \n                // 通知父组件刷新数据\n                this.$emit('getRefresh');\n                \n            } catch (error) {\n                Message({\n                    message: error.message || this.$t('MaterialPreparationBuild.SplitFailed'),\n                    type: 'error'\n                });\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.MaterialSplit {\n    .split-info-box {\n        margin-top: 10px;\n        padding: 10px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border: 1px solid #e4e7ed;\n        \n        .info-item {\n            display: flex;\n            margin-bottom: 5px;\n            \n            .info-label {\n                font-weight: 600;\n                color: #606266;\n                min-width: 120px;\n            }\n            \n            .info-value {\n                color: #303133;\n            }\n        }\n    }\n}\n</style>\n"]}]}
{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue", "mtime": 1749634401315}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BatchPallets.vue"], "names": [], "mappings": ";AAq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file": "BatchPallets.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxTitle\" style=\"font-size: 14px\">{{ $t('MaterialPreparationBuild.BatchPallets') }}</div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"inputformbox longwidthinput\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"BatchPallets\" :placeholder=\"$t('MaterialPreparationBuild.BatchPallets')\" @change=\"BatchPalletsChange\">\r\n                        <el-option v-for=\"(it, ind) in BatchPalletsOption\" :key=\"ind\" :label=\"it.ContainerName + '-' + it.ContainerState\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n                <el-button class=\"tablebtn\" size=\"small\" id=\"overBtn\" :disabled=\"myContainerState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getCompletePallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.CompletePallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"OpenPalletState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getOpenPallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.OpenPallet') }}\r\n                </el-button>\r\n                <div class=\"inputformbox btnselect\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"Operate\" :placeholder=\"$t('MaterialPreparationBuild.Operate')\" @change=\"OperateChange\">\r\n                        <el-option v-for=\"item in OperateList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                            <i :class=\"item.icon\"></i>\r\n                            <span style=\"float: right; font-size: 13px\">{{ item.label }}</span>\r\n                        </el-option>\r\n                    </el-select>\r\n                </div>\r\n                <div class=\"inputformbox\" style=\"width: 200px\">\r\n                    <el-checkbox v-model=\"materialonly\" @change=\"getTabelData()\">{{ $t('MaterialPreparationBuild.Currentmaterialonly') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"ClickPrint()\" size=\"small\" style=\"margin-left: 5px; width: 100px; position: absolute; right: 160px\" icon=\"el-icon-printer\">\r\n                    {{ this.$t('MaterialPreparationBuild.ReprintBtn') }}\r\n                </el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    :disabled=\"selectTabelData.length < 0\"\r\n                    size=\"small\"\r\n                    @click=\"GetRemoveBags()\"\r\n                    style=\"margin-left: 5px; width: 140px; position: absolute; right: 10px\"\r\n                    icon=\"el-icon-top\"\r\n                >\r\n                    {{ this.$t('MaterialPreparationBuild.RemoveBags') }}\r\n                </el-button>\r\n            </div>\r\n        </div>\r\n        <el-table :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"500\">\r\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n            <el-table-column\r\n                v-for=\"(item, index) in header\"\r\n                :fixed=\"item.fixed ? item.fixed : false\"\r\n                :key=\"index\"\r\n                :align=\"item.align\"\r\n                :prop=\"item.prop ? item.prop : item.value\"\r\n                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                :width=\"item.width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                    <span v-else-if=\"scope.column.property == 'Material'\">\r\n                        <div>{{ scope.row.MaterialCode }}</div>\r\n                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                        <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                            {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                        </div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                        <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                    <!-- <span v-else-if=\"scope.column.property == 'operate'\">\r\n                        <el-button size=\"mini\" class=\"operateBtn tablebtn\" @click=\"ClickPrint(scope.row)\" icon=\"el-icon-printer\">{{ $t('PalletList.Reprint') }}</el-button>\r\n                    </span> -->\r\n                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                </template>\r\n                <!-- <template slot-scope=\"scope\"></template> -->\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-dialog :title=\"$t('PalletList.Reprint')\" id=\"Printerdialog\" :visible.sync=\"PrinterModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Printerinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.Required ? item.label + ' *' : item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\"></el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.options\" :key=\"it.ID\" :label=\"it.Code\" :value=\"it.ID\"></el-option>\r\n                        </el-select>\r\n\r\n                        <div v-else-if=\"item.type == 'textArea'\" style=\"display: flex; align-items: center\">\r\n                            <el-input disabled v-model=\"item.value2\"></el-input>\r\n                            <span style=\"margin: 0 5px\">-</span>\r\n                            <el-input onkeyup=\"if(isNaN(value))execCommand('undo')\" onafterpaste=\"if(isNaN(value))execCommand('undo')\" @change=\"getValue3\" v-model=\"item.value3\"></el-input>\r\n                            <span style=\"margin: 0 5px\">=</span>\r\n                            <el-input disabled v-model=\"item.value\"></el-input>\r\n                            <span style=\"margin-left: 10px; width: 18%\">{{ item.unit }}</span>\r\n                        </div>\r\n                        <el-select clearable v-else-if=\"item.type == 'trea'\" v-model=\"item.value\" filterable @change=\"ChangeRemark\">\r\n                            <el-option v-for=\"(it, ind) in RemarkList\" :key=\"ind\" :label=\"it.ItemName\" :value=\"it.ItemName\"></el-option>\r\n                        </el-select>\r\n                        <!-- <el-input v-else-if=\"item.type == 'trea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input> -->\r\n                        <el-input v-else-if=\"item.type == 'textarea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-printer\" @click=\"getReprint()\">\r\n                    {{ $t('PalletList.Reprint') }}\r\n                </el-button>\r\n                <el-button @click=\"PrinterModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { GetPrinit7 } from '@/api/Inventory/common.js';\r\nimport { Message } from 'element-ui';\r\nimport { POBatchPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport $ from 'jquery';\r\nimport {\r\n    GetPageListMaterialPreDown,\r\n    CompletePallet,\r\n    OpenPallet,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    DeletePallet,\r\n    RemovePallet,\r\n    GetConsumlList,\r\n    PalletIsFinish_PG,\r\n    GetReprintSave\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            tableList: [],\r\n            tableId: 'INV_CLZB',\r\n            header: POBatchPalletsColumn,\r\n            selectTabelData: [],\r\n            BatchPallets: '',\r\n            BatchPalletsOption: [],\r\n            detailobj: {},\r\n            materialonly: false,\r\n            Operate: '',\r\n            InQuantity: '',\r\n            PrinterModel: false,\r\n            Printerinputlist: [\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.zts'),\r\n                    value: '',\r\n                    type: 'number',\r\n                    Required: true,\r\n                    id: 'Totalts'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.dqgxh'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'input',\r\n                    // id: 'XHNumber'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.ts'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'number',\r\n                    // id: 'TS'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.jsgs'),\r\n                    type: 'textArea',\r\n                    Required: true,\r\n                    value2: '',\r\n                    value3: '',\r\n                    value: '',\r\n                    unit: '',\r\n                    id: 'WNumber'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CommentsOption'),\r\n                    value: '',\r\n                    filterable: true,\r\n                    //    Required: true,\r\n                    type: 'trea',\r\n                    id: 'RemarkList'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    value: '',\r\n                    //    Required: true,\r\n                    type: 'textarea',\r\n                    id: 'Remark'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.xzdyj'),\r\n                    type: 'select',\r\n                    value: '',\r\n                    options: [],\r\n                    id: 'PrintID'\r\n                }\r\n            ],\r\n            OperateList: [\r\n                {\r\n                    value: 'add',\r\n                    label: this.$t('MaterialPreparationBuild.Addpallets'),\r\n                    icon: 'el-icon-plus'\r\n                },\r\n                {\r\n                    value: 'delete',\r\n                    label: this.$t('MaterialPreparationBuild.Deletepallets'),\r\n                    icon: 'el-icon-circle-close'\r\n                }\r\n            ],\r\n            myContainerState: true,\r\n            OpenPalletState: true,\r\n            RemarkList: []\r\n        };\r\n    },\r\n    async mounted() {\r\n        this.room = window.sessionStorage.getItem('room');\r\n        let Remark = await this.$getNewDataDictionary('PalletQuickFields');\r\n        this.RemarkList = Remark;\r\n        this.getSelectList();\r\n        this.getPrintList();\r\n    },\r\n    methods: {\r\n        ChangeRemark(val) {\r\n            this.Printerinputlist[5].value = this.Printerinputlist[5].value + val;\r\n        },\r\n        handleSelect(item) {\r\n            console.log(item);\r\n        },\r\n        async getPrintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res = await GetPrinit7(params);\r\n            this.Printerinputlist[this.Printerinputlist.length - 1].options = res.response;\r\n        },\r\n        ClickPrint() {\r\n            this.getValue1();\r\n        },\r\n        async getValue1() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                PID: this.detailobj.ProductionOrderId,\r\n                BatchId: this.detailobj.BatchId,\r\n                MCode: '7300030001' //水\r\n            };\r\n            let res = await GetConsumlList(params);\r\n            let data = res.response;\r\n            if (data) {\r\n                console.log(data.data);\r\n                if (data.dataCount == 0) {\r\n                    this.Printerinputlist[3].value2 = 0;\r\n                    this.Printerinputlist[3].unit = '';\r\n                } else {\r\n                    this.Printerinputlist[3].value2 = data.data[0].Quantity;\r\n                    this.Printerinputlist[3].unit = data.data[0].UName;\r\n                }\r\n            }\r\n            this.Printerinputlist[4].value = '';\r\n            this.Printerinputlist[5].value = '';\r\n            this.Printerinputlist[6].value = '';\r\n            if (this.Printerinputlist[6].options.length != 0) {\r\n                this.Printerinputlist[6].value = this.Printerinputlist[6].options[0].ID;\r\n            }\r\n            this.PrinterModel = true;\r\n        },\r\n        getValue3(val) {\r\n            if (this.Printerinputlist[3].value2 !== '') {\r\n                this.Printerinputlist[3].value = (Number(this.Printerinputlist[3].value2) - Number(this.Printerinputlist[3].value3)).toFixed(3);\r\n            }\r\n        },\r\n        //打印保存\r\n        async getReprint() {\r\n            console.log(this.BatchPallets);\r\n            this.detailobj = this.$parent.detailobj; //这里的this.detailobj就是工单的数据\r\n            let flag = this.Printerinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.Printerinputlist[3].value3 == '') {\r\n                flag = true;\r\n            } else {\r\n                flag = false;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let ContainerName = '';\r\n            let containerID = '';\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    containerID = item.ID;\r\n                    ContainerName = item.ContainerName;\r\n                }\r\n            });\r\n            let addobj = {};\r\n            this.Printerinputlist.forEach(item => {\r\n                addobj[item.id] = item.value;\r\n                if (item.value2) {\r\n                    addobj.unit = item.unit; //计算公式的单位\r\n\r\n                    addobj.WNumberText = item.value2 + '-' + item.value3 + '=' + item.value;\r\n                }\r\n            });\r\n            addobj.EquipmentId = this.room;\r\n            addobj.ContainerName = ContainerName; // 容器编号\r\n            addobj.ContainerID = containerID;\r\n            addobj.BatchId = this.detailobj.BatchId;\r\n            addobj.PID = this.detailobj.ProductionOrderId; //这里是示例，获取工单ID并且给传参PID赋值\r\n            let res = await GetReprintSave(addobj);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrinterModel = false;\r\n        },\r\n        async getCompletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let containerIDs = this.BatchPalletsOption.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                containerIDs: containerIDs,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                TagWeight: this.detailobj.MQuantityTotal == null ? 0 : Number(this.detailobj.MQuantityTotal),\r\n                UomID: this.detailobj.TUintid,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProID: this.detailobj.ProductionOrderId,\r\n                BatchID: this.detailobj.BatchId\r\n                // ProID:'',\r\n                // BatchID:''\r\n            };\r\n            let res = await CompletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getOpenPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                MaterialId: this.detailobj.MaterialId\r\n            };\r\n            let res = await OpenPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getSelectList() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            this.BatchPalletsOption = res.response;\r\n            this.BatchPallets = this.BatchPalletsOption[0].ID;\r\n            this.getBatchPalletsStatus();\r\n            this.getTabelData();\r\n        },\r\n        async getBatchPalletsStatus() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            console.log(this.detailobj, 666);\r\n            if (this.BatchPallets == '') {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = true;\r\n                return;\r\n            }\r\n\r\n            let flag = false;\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    if (item.ContainerState == 'complete' || item.ContainerState == '已完成' || item.ContainerState == '拼锅已完成') {\r\n                        flag = true;\r\n                        this.myContainerState = true;\r\n                        return false;\r\n                    } else {\r\n                        flag = false;\r\n                        this.myContainerState = false;\r\n                    }\r\n                }\r\n            });\r\n            if (flag) {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = false;\r\n\r\n                return false;\r\n            } else {\r\n                this.myContainerState = false;\r\n                this.OpenPalletState = true;\r\n            }\r\n            if (this.detailobj.MQuantity >= this.detailobj.MinPvalue && this.detailobj.MQuantity <= this.detailobj.MaxPvalue) {\r\n                this.myContainerState = false;\r\n            } else {\r\n                this.myContainerState = true;\r\n            }\r\n            let params = {\r\n                batchID: this.detailobj.BatchId,\r\n                eqpmentID: this.room\r\n            };\r\n            let res = await PalletIsFinish_PG(params);\r\n            if (res.msg != '失败') {\r\n                this.myContainerState = false;\r\n                if (this.myContainerState == false) {\r\n                    $('#overBtn').addClass('myfadeIn');\r\n                } else {\r\n                    $('#overBtn').removeClass('myfadeIn');\r\n                }\r\n            } else {\r\n                this.myContainerState = true;\r\n                $('#overBtn').removeClass('myfadeIn');\r\n            }\r\n        },\r\n        BatchPalletsChange() {\r\n            this.getBatchPalletsStatus();\r\n            window.sessionStorage.setItem('BatchPallets', this.BatchPallets);\r\n            this.getTabelData();\r\n        },\r\n        OperateChange(val) {\r\n            if (val == 'add') {\r\n                this.AddNewPallet();\r\n            } else if (val == 'delete') {\r\n                this.getDeletePallet();\r\n            }\r\n            this.Operate = '';\r\n        },\r\n        async GetRemoveBags() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            // alert(this.detailobj);\r\n            // console.log(this.detailobj);\r\n            let subIDs = this.selectTabelData.map(item => {\r\n                return item.SubId;\r\n            });\r\n            let params = {\r\n                subIDs: subIDs,\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await RemovePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getDeletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                ContainerId: this.BatchPallets,\r\n                actualWeight: this.InQuantity,\r\n                UomID: this.detailobj.TUintid\r\n            };\r\n            let res = await DeletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n\r\n        async AddNewPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getTabelData() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                mId: this.materialonly ? this.detailobj.MaterialId : '',\r\n                // MaterialId: this.detailobj.MaterialId,\r\n                ContainerId: this.BatchPallets,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListMaterialPreDown(params);\r\n            this.tableList = res.response.data;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.buildpalletsStart {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin: 10px 0;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 20%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .inputformbox {\r\n        width: 18vh;\r\n    }\r\n}\r\n#Printerdialog {\r\n    .colInputLabel {\r\n        margin-top: 10px;\r\n        font-weight: 600;\r\n    }\r\n    .el-select {\r\n        margin-top: 10px;\r\n        width: 100% !important;\r\n    }\r\n    .el-autocomplete {\r\n        width: 100% !important;\r\n    }\r\n}\r\n.buildpalletsStart .longwidthinput .el-select {\r\n    width: 100%;\r\n}\r\n\r\n.buildpalletsStart .btnselect .el-input__inner {\r\n    // background: #3dcd58;\r\n    ::placeholder {\r\n        color: #fff;\r\n    }\r\n}\r\n</style>\r\n"]}]}
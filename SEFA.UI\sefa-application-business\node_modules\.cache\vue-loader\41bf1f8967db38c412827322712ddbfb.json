{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue?vue&type=style&index=0&id=932d5cba&lang=scss&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue", "mtime": 1749634401315}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYnVpbGRwYWxsZXRzU3RhcnQgew0KICAgIC5kaWFsb2dkZXRhaWxib3ggew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgbWFyZ2luOiAxMHB4IDA7DQogICAgICAgIC5kaWFsb2dkZXRhaWxzaW5nbGVsYWJlbCB7DQogICAgICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICAgICAgd2lkdGg6IDIwJTsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICB9DQoNCiAgICAgICAgLmRpYWxvZ2RldGFpbHNpbmdsZXZhbHVlIHsNCiAgICAgICAgICAgIHdpZHRoOiA3OCU7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgICAgICAgfQ0KICAgIH0NCiAgICAuaW5wdXRmb3JtYm94IHsNCiAgICAgICAgd2lkdGg6IDE4dmg7DQogICAgfQ0KfQ0KI1ByaW50ZXJkaWFsb2cgew0KICAgIC5jb2xJbnB1dExhYmVsIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICB9DQogICAgLmVsLXNlbGVjdCB7DQogICAgICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgfQ0KICAgIC5lbC1hdXRvY29tcGxldGUgew0KICAgICAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICAgIH0NCn0NCi5idWlsZHBhbGxldHNTdGFydCAubG9uZ3dpZHRoaW5wdXQgLmVsLXNlbGVjdCB7DQogICAgd2lkdGg6IDEwMCU7DQp9DQoNCi5idWlsZHBhbGxldHNTdGFydCAuYnRuc2VsZWN0IC5lbC1pbnB1dF9faW5uZXIgew0KICAgIC8vIGJhY2tncm91bmQ6ICMzZGNkNTg7DQogICAgOjpwbGFjZWhvbGRlciB7DQogICAgICAgIGNvbG9yOiAjZmZmOw0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["BatchPallets.vue"], "names": [], "mappings": ";AA2gBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BatchPallets.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxTitle\" style=\"font-size: 14px\">{{ $t('MaterialPreparationBuild.BatchPallets') }}</div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"inputformbox longwidthinput\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"BatchPallets\" :placeholder=\"$t('MaterialPreparationBuild.BatchPallets')\" @change=\"BatchPalletsChange\">\r\n                        <el-option v-for=\"(it, ind) in BatchPalletsOption\" :key=\"ind\" :label=\"it.ContainerName + '-' + it.ContainerState\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n                <el-button class=\"tablebtn\" size=\"small\" id=\"overBtn\" :disabled=\"myContainerState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getCompletePallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.CompletePallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"OpenPalletState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getOpenPallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.OpenPallet') }}\r\n                </el-button>\r\n                <div class=\"inputformbox btnselect\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"Operate\" :placeholder=\"$t('MaterialPreparationBuild.Operate')\" @change=\"OperateChange\">\r\n                        <el-option v-for=\"item in OperateList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                            <i :class=\"item.icon\"></i>\r\n                            <span style=\"float: right; font-size: 13px\">{{ item.label }}</span>\r\n                        </el-option>\r\n                    </el-select>\r\n                </div>\r\n                <div class=\"inputformbox\" style=\"width: 200px\">\r\n                    <el-checkbox v-model=\"materialonly\" @change=\"getTabelData()\">{{ $t('MaterialPreparationBuild.Currentmaterialonly') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"ClickPrint()\" size=\"small\" style=\"margin-left: 5px; width: 100px; position: absolute; right: 160px\" icon=\"el-icon-printer\">\r\n                    {{ this.$t('MaterialPreparationBuild.ReprintBtn') }}\r\n                </el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    :disabled=\"selectTabelData.length < 0\"\r\n                    size=\"small\"\r\n                    @click=\"GetRemoveBags()\"\r\n                    style=\"margin-left: 5px; width: 140px; position: absolute; right: 10px\"\r\n                    icon=\"el-icon-top\"\r\n                >\r\n                    {{ this.$t('MaterialPreparationBuild.RemoveBags') }}\r\n                </el-button>\r\n            </div>\r\n        </div>\r\n        <el-table :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"500\">\r\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n            <el-table-column\r\n                v-for=\"(item, index) in header\"\r\n                :fixed=\"item.fixed ? item.fixed : false\"\r\n                :key=\"index\"\r\n                :align=\"item.align\"\r\n                :prop=\"item.prop ? item.prop : item.value\"\r\n                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                :width=\"item.width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                    <span v-else-if=\"scope.column.property == 'Material'\">\r\n                        <div>{{ scope.row.MaterialCode }}</div>\r\n                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                        <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                            {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                        </div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                        <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                    <!-- <span v-else-if=\"scope.column.property == 'operate'\">\r\n                        <el-button size=\"mini\" class=\"operateBtn tablebtn\" @click=\"ClickPrint(scope.row)\" icon=\"el-icon-printer\">{{ $t('PalletList.Reprint') }}</el-button>\r\n                    </span> -->\r\n                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                </template>\r\n                <!-- <template slot-scope=\"scope\"></template> -->\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-dialog :title=\"$t('PalletList.Reprint')\" id=\"Printerdialog\" :visible.sync=\"PrinterModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Printerinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.Required ? item.label + ' *' : item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\"></el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.options\" :key=\"it.ID\" :label=\"it.Code\" :value=\"it.ID\"></el-option>\r\n                        </el-select>\r\n\r\n                        <div v-else-if=\"item.type == 'textArea'\" style=\"display: flex; align-items: center\">\r\n                            <el-input disabled v-model=\"item.value2\"></el-input>\r\n                            <span style=\"margin: 0 5px\">-</span>\r\n                            <el-input onkeyup=\"if(isNaN(value))execCommand('undo')\" onafterpaste=\"if(isNaN(value))execCommand('undo')\" @change=\"getValue3\" v-model=\"item.value3\"></el-input>\r\n                            <span style=\"margin: 0 5px\">=</span>\r\n                            <el-input disabled v-model=\"item.value\"></el-input>\r\n                            <span style=\"margin-left: 10px; width: 18%\">{{ item.unit }}</span>\r\n                        </div>\r\n                        <el-select clearable v-else-if=\"item.type == 'trea'\" v-model=\"item.value\" filterable @change=\"ChangeRemark\">\r\n                            <el-option v-for=\"(it, ind) in RemarkList\" :key=\"ind\" :label=\"it.ItemName\" :value=\"it.ItemName\"></el-option>\r\n                        </el-select>\r\n                        <!-- <el-input v-else-if=\"item.type == 'trea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input> -->\r\n                        <el-input v-else-if=\"item.type == 'textarea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-printer\" @click=\"getReprint()\">\r\n                    {{ $t('PalletList.Reprint') }}\r\n                </el-button>\r\n                <el-button @click=\"PrinterModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { GetPrinit7 } from '@/api/Inventory/common.js';\r\nimport { Message } from 'element-ui';\r\nimport { POBatchPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport $ from 'jquery';\r\nimport {\r\n    GetPageListMaterialPreDown,\r\n    CompletePallet,\r\n    OpenPallet,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    DeletePallet,\r\n    RemovePallet,\r\n    GetConsumlList,\r\n    PalletIsFinish_PG,\r\n    GetReprintSave\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            tableList: [],\r\n            tableId: 'INV_CLZB',\r\n            header: POBatchPalletsColumn,\r\n            selectTabelData: [],\r\n            BatchPallets: '',\r\n            BatchPalletsOption: [],\r\n            detailobj: {},\r\n            materialonly: false,\r\n            Operate: '',\r\n            InQuantity: '',\r\n            PrinterModel: false,\r\n            Printerinputlist: [\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.zts'),\r\n                    value: '',\r\n                    type: 'number',\r\n                    Required: true,\r\n                    id: 'Totalts'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.dqgxh'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'input',\r\n                    // id: 'XHNumber'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.ts'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'number',\r\n                    // id: 'TS'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.jsgs'),\r\n                    type: 'textArea',\r\n                    Required: true,\r\n                    value2: '',\r\n                    value3: '',\r\n                    value: '',\r\n                    unit: '',\r\n                    id: 'WNumber'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CommentsOption'),\r\n                    value: '',\r\n                    filterable: true,\r\n                    //    Required: true,\r\n                    type: 'trea',\r\n                    id: 'RemarkList'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    value: '',\r\n                    //    Required: true,\r\n                    type: 'textarea',\r\n                    id: 'Remark'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.xzdyj'),\r\n                    type: 'select',\r\n                    value: '',\r\n                    options: [],\r\n                    id: 'PrintID'\r\n                }\r\n            ],\r\n            OperateList: [\r\n                {\r\n                    value: 'add',\r\n                    label: this.$t('MaterialPreparationBuild.Addpallets'),\r\n                    icon: 'el-icon-plus'\r\n                },\r\n                {\r\n                    value: 'delete',\r\n                    label: this.$t('MaterialPreparationBuild.Deletepallets'),\r\n                    icon: 'el-icon-circle-close'\r\n                }\r\n            ],\r\n            myContainerState: true,\r\n            OpenPalletState: true,\r\n            RemarkList: []\r\n        };\r\n    },\r\n    async mounted() {\r\n        this.room = window.sessionStorage.getItem('room');\r\n        let Remark = await this.$getNewDataDictionary('PalletQuickFields');\r\n        this.RemarkList = Remark;\r\n        this.getSelectList();\r\n        this.getPrintList();\r\n    },\r\n    methods: {\r\n        ChangeRemark(val) {\r\n            this.Printerinputlist[5].value = this.Printerinputlist[5].value + val;\r\n        },\r\n        handleSelect(item) {\r\n            console.log(item);\r\n        },\r\n        async getPrintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res = await GetPrinit7(params);\r\n            this.Printerinputlist[this.Printerinputlist.length - 1].options = res.response;\r\n        },\r\n        ClickPrint() {\r\n            this.getValue1();\r\n        },\r\n        async getValue1() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                PID: this.detailobj.ProductionOrderId,\r\n                BatchId: this.detailobj.BatchId,\r\n                MCode: '7300030001' //水\r\n            };\r\n            let res = await GetConsumlList(params);\r\n            let data = res.response;\r\n            if (data) {\r\n                console.log(data.data);\r\n                if (data.dataCount == 0) {\r\n                    this.Printerinputlist[3].value2 = 0;\r\n                    this.Printerinputlist[3].unit = '';\r\n                } else {\r\n                    this.Printerinputlist[3].value2 = data.data[0].Quantity;\r\n                    this.Printerinputlist[3].unit = data.data[0].UName;\r\n                }\r\n            }\r\n            this.Printerinputlist[4].value = '';\r\n            this.Printerinputlist[5].value = '';\r\n            this.Printerinputlist[6].value = '';\r\n            if (this.Printerinputlist[6].options.length != 0) {\r\n                this.Printerinputlist[6].value = this.Printerinputlist[6].options[0].ID;\r\n            }\r\n            this.PrinterModel = true;\r\n        },\r\n        getValue3(val) {\r\n            if (this.Printerinputlist[3].value2 !== '') {\r\n                this.Printerinputlist[3].value = (Number(this.Printerinputlist[3].value2) - Number(this.Printerinputlist[3].value3)).toFixed(3);\r\n            }\r\n        },\r\n        //打印保存\r\n        async getReprint() {\r\n            console.log(this.BatchPallets);\r\n            this.detailobj = this.$parent.detailobj; //这里的this.detailobj就是工单的数据\r\n            let flag = this.Printerinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.Printerinputlist[3].value3 == '') {\r\n                flag = true;\r\n            } else {\r\n                flag = false;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let ContainerName = '';\r\n            let containerID = '';\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    containerID = item.ID;\r\n                    ContainerName = item.ContainerName;\r\n                }\r\n            });\r\n            let addobj = {};\r\n            this.Printerinputlist.forEach(item => {\r\n                addobj[item.id] = item.value;\r\n                if (item.value2) {\r\n                    addobj.unit = item.unit; //计算公式的单位\r\n\r\n                    addobj.WNumberText = item.value2 + '-' + item.value3 + '=' + item.value;\r\n                }\r\n            });\r\n            addobj.EquipmentId = this.room;\r\n            addobj.ContainerName = ContainerName; // 容器编号\r\n            addobj.ContainerID = containerID;\r\n            addobj.BatchId = this.detailobj.BatchId;\r\n            addobj.PID = this.detailobj.ProductionOrderId; //这里是示例，获取工单ID并且给传参PID赋值\r\n            let res = await GetReprintSave(addobj);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrinterModel = false;\r\n        },\r\n        async getCompletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let containerIDs = this.BatchPalletsOption.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                containerIDs: containerIDs,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                TagWeight: this.detailobj.MQuantityTotal == null ? 0 : Number(this.detailobj.MQuantityTotal),\r\n                UomID: this.detailobj.TUintid,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProID: this.detailobj.ProductionOrderId,\r\n                BatchID: this.detailobj.BatchId\r\n                // ProID:'',\r\n                // BatchID:''\r\n            };\r\n            let res = await CompletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getOpenPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                MaterialId: this.detailobj.MaterialId\r\n            };\r\n            let res = await OpenPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getSelectList() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            this.BatchPalletsOption = res.response;\r\n            this.BatchPallets = this.BatchPalletsOption[0].ID;\r\n            this.getBatchPalletsStatus();\r\n            this.getTabelData();\r\n        },\r\n        async getBatchPalletsStatus() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            console.log(this.detailobj, 666);\r\n            if (this.BatchPallets == '') {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = true;\r\n                return;\r\n            }\r\n\r\n            let flag = false;\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    if (item.ContainerState == 'complete' || item.ContainerState == '已完成' || item.ContainerState == '拼锅已完成') {\r\n                        flag = true;\r\n                        this.myContainerState = true;\r\n                        return false;\r\n                    } else {\r\n                        flag = false;\r\n                        this.myContainerState = false;\r\n                    }\r\n                }\r\n            });\r\n            if (flag) {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = false;\r\n\r\n                return false;\r\n            } else {\r\n                this.myContainerState = false;\r\n                this.OpenPalletState = true;\r\n            }\r\n            if (this.detailobj.MQuantity >= this.detailobj.MinPvalue && this.detailobj.MQuantity <= this.detailobj.MaxPvalue) {\r\n                this.myContainerState = false;\r\n            } else {\r\n                this.myContainerState = true;\r\n            }\r\n            let params = {\r\n                batchID: this.detailobj.BatchId,\r\n                eqpmentID: this.room\r\n            };\r\n            let res = await PalletIsFinish_PG(params);\r\n            if (res.msg != '失败') {\r\n                this.myContainerState = false;\r\n                if (this.myContainerState == false) {\r\n                    $('#overBtn').addClass('myfadeIn');\r\n                } else {\r\n                    $('#overBtn').removeClass('myfadeIn');\r\n                }\r\n            } else {\r\n                this.myContainerState = true;\r\n                $('#overBtn').removeClass('myfadeIn');\r\n            }\r\n        },\r\n        BatchPalletsChange() {\r\n            this.getBatchPalletsStatus();\r\n            window.sessionStorage.setItem('BatchPallets', this.BatchPallets);\r\n            this.getTabelData();\r\n        },\r\n        OperateChange(val) {\r\n            if (val == 'add') {\r\n                this.AddNewPallet();\r\n            } else if (val == 'delete') {\r\n                this.getDeletePallet();\r\n            }\r\n            this.Operate = '';\r\n        },\r\n        async GetRemoveBags() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            // alert(this.detailobj);\r\n            // console.log(this.detailobj);\r\n            let subIDs = this.selectTabelData.map(item => {\r\n                return item.SubId;\r\n            });\r\n            let params = {\r\n                subIDs: subIDs,\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await RemovePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getDeletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                ContainerId: this.BatchPallets,\r\n                actualWeight: this.InQuantity,\r\n                UomID: this.detailobj.TUintid\r\n            };\r\n            let res = await DeletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n\r\n        async AddNewPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getTabelData() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                mId: this.materialonly ? this.detailobj.MaterialId : '',\r\n                // MaterialId: this.detailobj.MaterialId,\r\n                ContainerId: this.BatchPallets,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListMaterialPreDown(params);\r\n            this.tableList = res.response.data;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.buildpalletsStart {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin: 10px 0;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 20%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .inputformbox {\r\n        width: 18vh;\r\n    }\r\n}\r\n#Printerdialog {\r\n    .colInputLabel {\r\n        margin-top: 10px;\r\n        font-weight: 600;\r\n    }\r\n    .el-select {\r\n        margin-top: 10px;\r\n        width: 100% !important;\r\n    }\r\n    .el-autocomplete {\r\n        width: 100% !important;\r\n    }\r\n}\r\n.buildpalletsStart .longwidthinput .el-select {\r\n    width: 100%;\r\n}\r\n\r\n.buildpalletsStart .btnselect .el-input__inner {\r\n    // background: #3dcd58;\r\n    ::placeholder {\r\n        color: #fff;\r\n    }\r\n}\r\n</style>\r\n"]}]}
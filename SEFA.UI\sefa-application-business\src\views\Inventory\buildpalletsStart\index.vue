<template>
    <div class="usemystyle buildpalletsStart">
        <div class="InventorySearchBox" style="margin-bottom: 0">
            <div class="searchbox">
                <el-button style="margin-left: 5px; width: 160px" size="small" icon="el-icon-back" @click="back()">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>
                <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="refresh()">{{ this.$t('Inventory.refresh') }}</el-button>
                <div class="searchtipbox">
                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}
                </div>
                <el-button class="tablebtn" @click="GetAddPallet" v-if="this.SelectList.length == 0 && way == 'Batch'" size="small" style="margin-left: 5px; width: 120px" icon="el-icon-plus">
                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}
                </el-button>
                <el-button class="tablebtn" @click="openKeyDown()" size="small" style="margin-left: 5px">
                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}
                </el-button>
                <div class="searchtipbox" style="background: #fff; color: red">计划备注：{{ detailobj.Remark }}</div>
                <div class="rightsearchbox" style="position: absolute; right: 10px; display: flex">
                    <el-button style="margin-left: 5px; width: 100px" size="small" icon="el-icon-back" :disabled="!MaterialList[MaterialNow - 1]" @click="ChangeMaterial(-1)">
                        {{ this.$t('MaterialPreparationBuild.Previous') }}
                    </el-button>
                    <el-button style="margin-left: 0px; width: 130px" size="small" icon="el-icon-right" :disabled="!MaterialList[MaterialNow + 1]" @click="ChangeMaterial(+1)">
                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}
                    </el-button>
                </div>
            </div>
            <div class="searchbox">
                <!-- <div class="searchboxTitle" v-if="way == 'Material'">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->
                <div class="searchboxTitle">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>
                <div class="searchboxTitle">
                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/
                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}
                    <!-- /{{ detailobj.PrepStatuscount }}  -->
                    <!-- -{{ detailobj.EquipmentName }} -->
                </div>
            </div>
            <div class="searchbox">
                <div class="searchboxColorTitle" v-if="way == 'Batch'" :style="{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}
                </div>

                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{
                        way == 'Material'
                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0
                                ? detailobj.MQuantity
                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))
                            : detailobj.MQuantity
                    }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{
                        way == 'Material'
                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0
                                ? detailobj.MQuantityTotal
                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))
                            : detailobj.MQuantityTotal
                    }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}
                </div>
            </div>
        </div>
        <div class="tableboxheightall">
            <div class="tablebox adaptive-inventory-container" :style="{
                '--table-height': tableHeight + 'px',
                '--dynamic-table-height': tableHeight + 'px'
            }">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">
                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}
                            <!-- <span style="font-size: 12px; color: #666; margin-left: 10px;">
                                ({{ tableList.length }}行 | {{ tableHeight }}px | {{ useViewportHeight ? '视口模式' : '行数模式' }})
                            </span> -->
                        </div>
                        <div style="position: absolute; right: 10px; display: flex; gap: 5px; align-items: center;">
                            <!-- 高度模式切换按钮 -->
                            <el-button
                                size="mini"
                                :type="useViewportHeight ? 'primary' : 'default'"
                                @click="toggleHeightMode()"
                                :title="useViewportHeight ? '当前：视口模式，点击切换到行数模式' : '当前：行数模式，点击切换到视口模式'"
                                style="margin-right: 5px;">
                                {{ useViewportHeight ? '视口' : '行数' }}
                            </el-button>

                            <!-- 打印按钮 -->
                            <el-button v-if="way == 'Material'" class="tablebtn" size="small" style="width: 140px;" @click="PrintAvallable()">
                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 自适应高度表格 -->
                <el-table
                    :data="tableList"
                    ref="TopTabel"
                    @row-click="GetCurrentRow"
                    highlight-current-row
                    :class="['adaptive-table', `height-mode-${useViewportHeight ? 'viewport' : 'rows'}`]"
                    :style="{
                        width: '100%',
                        height: tableHeight + 'px',
                        maxHeight: tableHeight + 'px',
                        minHeight: tableHeight + 'px'
                    }"
                    :height="tableHeight"
                    :max-height="tableHeight"
                    :key="tableRenderKey"
                    size="small">
                    <el-table-column
                        v-for="(item, index) in header"
                        :fixed="item.fixed ? item.fixed : false"
                        :key="index"
                        :align="item.align"
                        :prop="item.prop ? item.prop : item.value"
                        :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                        :width="item.width"
                    >
                        <template slot-scope="scope">
                            <span v-if="scope.column.property == 'BatchStatus'">
                                <div :class="'statusbox batchstatus' + scope.row.LStatus">
                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'SSCCStatus'">
                                <div :class="'statusbox status' + scope.row.SbStatus">
                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>

                            <span v-else-if="scope.column.property == 'ExpirationDate'">
                                <div class="statusbox" :style="{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }">
                                    {{ scope.row.ExpirationDate }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- <div class="tablebox" style="height: 32%" v-if="way == 'Batch'">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">{{ $t('MaterialPreparationBuild.POInventory') }}</div>
                    </div>
                </div>
                <el-table :data="tableListBatchPO" ref="TopBatchTabel" @row-click="GetCurrentRow2" highlight-current-row style="width: 100%" height="200">
                    <el-table-column
                        v-for="(item, index) in headerBatchPO"
                        :fixed="item.fixed ? item.fixed : false"
                        :key="index"
                        :align="item.align"
                        :prop="item.prop ? item.prop : item.value"
                        :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                        :width="item.width"
                    >
                        <template slot-scope="scope">
                            <span v-if="scope.column.property == 'SSCC/Batch'">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>
                            <span v-else-if="scope.column.property == 'Material'">
                                <div>{{ scope.row.MaterialCode }}</div>
                                <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'SbStatus'">
                                <div :class="'statusbox status' + scope.row.SbStatus">
                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'LStatus'">
                                <div :class="'statusbox batchstatus' + scope.row.LStatus">
                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'ExpirationDate'">
                                <div class="statusbox" :style="{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }">{{ scope.row.ExpirationDate }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div> -->
            <div class="tablebox" style="height: 21%">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>
                    </div>
                </div>
                <el-tabs v-model="activeName" type="border-card">
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.FullBag')" name="FullBag">
                        <FullBag ref="FullBag" @getRefresh="refresh()" @getRowSSCC="GetSSCC" @getRowBySscc="getRowBySscc"></FullBag>
                    </el-tab-pane>
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.PartialBag')" name="PartialBag">
                        <PartialBag ref="PartialBag" @getRefresh="refresh()" @getRowBySscc="getRowBySscc"></PartialBag>
                    </el-tab-pane>
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.FullAmount')" name="FullAmount">
                        <FullAmount ref="FullAmount" @getRefresh="refresh()" @getRowSSCC="GetSSCC" @getRowBySscc="getRowBySscc"></FullAmount>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class="tablebox" style="height: 600px" v-if="this.SelectList.length != 0 && way == 'Batch'">
                <BatchPallets ref="BatchPallets"></BatchPallets>
            </div>
            <div class="tablebox" style="height: 600px" v-if="way == 'Material'">
                <POInventory ref="POInventory"></POInventory>
            </div>
        </div>
        <el-dialog :title="$t('Inventory.Print')" id="Printdialog" :visible.sync="PrintModel" width="500px">
            <div class="dialogdetailbox" style="margin: 10px 0">
                <div class="dialogdetailsinglelabel">{{ $t('Inventory.selectprinter') }}</div>
                <div class="dialogdetailsinglevalue" style="width: auto">
                    <el-select disabled clearable v-model="PrintId" filterable>
                        <el-option v-for="item in printeroption" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue"></el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-orange" @click="getPrint()">
                    {{ $t('Inventory.Print') }}
                </el-button>
                <el-button @click="PrintModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/views/Inventory/mystyle.scss';
import { Message, MessageBox } from 'element-ui';
import { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';
import {
    GetPageListNewMaterialPreDown,
    GetPageListByMaterial,
    GetPageListByBatchIDSByID,
    GetPageListByMaterialII,
    GetPageListByBatchIDSII,
    GetPageListMaterialPreTop,
    GetPageListNewMaterialPreTop,
    GetConSelectList,
    FirstAddPallet,
    GetPageListByBatchIDS,
    MygetSSCC
} from '@/api/Inventory/MaterialPreparation.js';
import { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';
import { Empty } from 'ant-design-vue';

export default {
    components: {
        PartialBag: () => import('./components/PartialBag'),
        FullAmount: () => import('./components/FullAmount'),
        FullBag: () => import('./components/FullBag'),
        POInventory: () => import('./components/POInventory'),
        BatchPallets: () => import('./components/BatchPallets')
    },
    data() {
        return {
            PrintId: '',
            printeroption: [],
            PrintModel: false,
            isExpirationDate: false,
            tableId: 'INV_CLZB',
            activeName: 'PartialBag',
            OnlyFullAmount: false,
            Hidecompleted: false,
            detailobj: {},
            room: '',
            tableList: [],
            tableListBatchPO: [],
            SelectList: [],
            headerBatchPO: POInventoryPalletsColumn,
            header: AvallableInventoryColumn,
            way: '',
            listId: '',
            nowChooseRow: {},
            MaterialList: [],
            MaterialNow: null,
            UseType: '',
            clblFlag: false,
            keepKeyDown: false,

            // 🎯 表格自适应高度配置
            minTableHeight: 180,        // 最小高度（表头+至少2行）
            maxTableHeight: 500,        // 最大高度
            baseRowHeight: 42,          // 基础行高
            actualRowHeight: 42,        // 实际检测到的行高
            headerHeight: 40,           // 表头高度
            windowHeight: window.innerHeight,
            useViewportHeight: false,   // 默认使用行数模式
            tableHeight: 220,           // 当前表格高度
            tableRenderKey: 0,          // 强制重新渲染
            isCalculating: false,       // 防止重复计算
            resizeTimer: null           // 防抖定时器
        };
    },
    mounted() {
        console.log(this.$route)
        console.log(this.$route.path)
        let mykey = window.sessionStorage.getItem('MaterialPreparation');
        if (mykey == 'clbl') {
            this.way = 'Material';
        } else {
            this.way = 'Batch';
        }
        this.detailobj = JSON.parse(this.$route.query.query);
        console.log(this.detailobj, 123);
        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);
        console.log(this.clblFlag);
        this.UseType = this.$route.query.UseType;
        this.listId = JSON.parse(this.$route.query.List);
        this.room = window.sessionStorage.getItem('room');
        this.getprintList();
        // this.way = this.$route.query.way;
        this.GetMaterialList();
        this.getSelectList();
        this.getTopData();
        if (this.way == 'Batch') {
            this.getPOTabelData();
            this.activeName = 'FullBag';
        } else {
            this.activeName = 'PartialBag';
        }

        // 🎯 初始化表格自适应高度功能
        this.initAdaptiveHeight();
    },
    beforeMount() {
        window.removeEventListener('keyup', this.getKeyDown);
    },
    beforeDestroy() {
        // 清理事件监听器
        window.removeEventListener('keyup', this.getKeyDown);
        if (this.handleResize) {
            window.removeEventListener('resize', this.handleResize);
        }
        if (this.resizeTimer) {
            clearTimeout(this.resizeTimer);
        }
    },
    methods: {
        openKeyDown() {
            console.log(2);
            if (this.keepKeyDown == false) {
                window.addEventListener('keyup', this.getKeyDown);
                this.keepKeyDown = true;
            } else {
                window.removeEventListener('keyup', this.getKeyDown);
                this.keepKeyDown = false;
            }
        },
        getKeyDown(event) {
            if (this.keepKeyDown) {
                let code = event.keyCode;
                console.log(code);
                switch (code) {
                    case 37:
                        if (this.MaterialList[this.MaterialNow - 1]) {
                            this.ChangeMaterial(-1);
                        } else {
                            Message({
                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,
                                type: 'warning'
                            });
                        }
                        break;
                    case 39:
                        if (this.MaterialList[this.MaterialNow + 1]) {
                            this.ChangeMaterial(+1);
                        } else {
                            Message({
                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,
                                type: 'warning'
                            });
                        }
                        break;
                    case 32:
                        if (this.activeName == 'FullBag') {
                            console.log('FullBag');
                            if (this.$refs.FullBag.getbtnStatus()) {
                                this.$refs.FullBag.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        if (this.activeName == 'PartialBag') {
                            console.log('PartialBag');
                            if (this.$refs.PartialBag.getbtnStatus()) {
                                this.$refs.PartialBag.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        if (this.activeName == 'FullAmount') {
                            console.log('FullAmount');
                            if (this.$refs.FullAmount.getbtnStatus()) {
                                this.$refs.FullAmount.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        break;
                }
                return false;
            }
        },
        async getPrint() {
            let ids = this.tableList.map(item => {
                return item.ID;
            });

            // let params = {
            //     Ids: ids,
            //     equmentid: this.room,
            //     PrintId: this.PrintId
            // };

            let params = {
                printId: this.PrintId,
                EquipmentId: this.room,
                BagSiZe: this.detailobj.BagSize,
                MCode: this.detailobj.MCode,
                ids: ids
            };

            let res = await PrintPreparaLabelKY(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.PrintModel = false;
        },

        // 🎯 表格自适应高度核心方法
        initAdaptiveHeight() {
            console.log('=== 初始化表格自适应高度 ===');

            // 添加窗口大小变化监听器
            this.handleResize = () => {
                if (this.resizeTimer) {
                    clearTimeout(this.resizeTimer);
                }
                this.resizeTimer = setTimeout(() => {
                    this.windowHeight = window.innerHeight;
                    if (this.useViewportHeight) {
                        this.calculateTableHeight();
                    }
                }, 300);
            };
            window.addEventListener('resize', this.handleResize);

            // 初始化表格高度
            this.$nextTick(() => {
                this.calculateTableHeight();
            });
        },

        // 计算表格高度
        calculateTableHeight() {
            if (this.isCalculating) return;
            this.isCalculating = true;

            console.log('=== 计算表格高度 ===');
            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');
            console.log('数据行数:', this.tableList ? this.tableList.length : 0);

            let newHeight;

            if (!this.tableList || this.tableList.length === 0) {
                newHeight = this.minTableHeight;
                console.log('无数据，使用最小高度:', newHeight);
            } else if (this.useViewportHeight) {
                // 视口模式：基于窗口高度的25%
                const availableHeight = this.windowHeight * 0.25;
                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);
                console.log('视口模式计算:', {
                    windowHeight: this.windowHeight,
                    availableHeight,
                    newHeight
                });
            } else {
                // 行数模式：基于数据行数智能计算
                newHeight = this.calculateRowBasedHeight();
            }

            // 更新高度
            if (Math.abs(this.tableHeight - newHeight) > 2) {
                this.tableHeight = newHeight;
                this.tableRenderKey++;
                console.log('高度已更新:', this.tableHeight, 'px');

                this.$nextTick(() => {
                    this.applyTableHeight();
                });
            }

            this.isCalculating = false;
        },

        // 基于行数计算高度
        calculateRowBasedHeight() {
            const padding = 8;
            const borderHeight = 2;

            // 检测实际行高
            this.detectActualRowHeight();

            // 计算总高度：表头 + 数据行 + 边距
            const totalHeight = this.headerHeight +
                               (this.tableList.length * this.actualRowHeight) +
                               padding +
                               borderHeight;

            const finalHeight = Math.min(Math.max(totalHeight, this.minTableHeight), this.maxTableHeight);

            console.log('行数模式计算:', {
                headerHeight: this.headerHeight,
                dataRows: this.tableList.length,
                actualRowHeight: this.actualRowHeight,
                totalHeight,
                finalHeight
            });

            return finalHeight;
        },

        // 检测实际行高
        detectActualRowHeight() {
            if (this.$refs.TopTabel && this.tableList.length > 0) {
                this.$nextTick(() => {
                    const tableEl = this.$refs.TopTabel.$el;
                    const firstRow = tableEl.querySelector('.el-table__body tr');
                    if (firstRow) {
                        const detectedHeight = firstRow.offsetHeight;
                        if (detectedHeight > 0 && Math.abs(detectedHeight - this.actualRowHeight) > 2) {
                            console.log('检测到新的行高:', detectedHeight, '(原:', this.actualRowHeight, ')');
                            this.actualRowHeight = detectedHeight;
                        }
                    }
                });
            }
        },

        // 应用表格高度到DOM
        applyTableHeight() {
            if (this.$refs.TopTabel) {
                const tableComponent = this.$refs.TopTabel;
                const tableEl = tableComponent.$el;

                // 设置表格高度
                tableEl.style.height = this.tableHeight + 'px';
                tableEl.style.maxHeight = this.tableHeight + 'px';
                tableEl.style.minHeight = this.tableHeight + 'px';

                // 设置表格内容区域高度
                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');
                if (bodyWrapper) {
                    bodyWrapper.style.maxHeight = (this.tableHeight - this.headerHeight) + 'px';
                }

                // 调用Element UI的布局方法
                if (tableComponent.doLayout) {
                    tableComponent.doLayout();
                }

                console.log('DOM高度已应用:', this.tableHeight + 'px');
            }
        },

        // 切换高度模式
        toggleHeightMode() {
            this.useViewportHeight = !this.useViewportHeight;
            console.log('切换到:', this.useViewportHeight ? '视口模式' : '行数模式');

            Message({
                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,
                type: 'success',
                duration: 2000
            });

            this.calculateTableHeight();
        },

        async getprintList() {
            let params = {
                equipmentId: this.room
            };
            let res2 = await GetPrinit8(params);
            res2.response.forEach(item => {
                item.value = item.ID;
                item.label = item.Code;
                item.ItemName = item.Code;
                item.ItemValue = item.ID;
            });
            this.printeroption = res2.response;
            if (this.$refs.POInventory) {
                this.$refs.POInventory.printeroption = this.printeroption;
            }
        },
        PrintAvallable() {
            this.PrintId = window.sessionStorage.getItem('PrintId');
            this.PrintModel = true;
        },
        async getPOTabelData() {
            let params = {
                BatchId: this.detailobj.BatchId,
                ProOrderid: this.detailobj.ProductionOrderId,
                MaterialId: this.detailobj.MaterialId,
                EquipmentId: this.room,
                pageIndex: 1,
                pageSize: 1000
            };
            let res = await GetPageListNewMaterialPreDown(params);
            this.tableListBatchPO = res.response.data;
        },
        GetSSCC(key) {
            let flag = this.tableList.some(item => {
                return item.SbSscc == key;
            });
            if (flag == true) {
                if (this.$refs.FullBag) {
                    this.$refs.FullBag.ssccFlag = true;
                }
                if (this.$refs.PartialBag) {
                    this.$refs.PartialBag.ssccFlag = true;
                }
                if (this.$refs.FullAmount) {
                    this.$refs.FullAmount.ssccFlag = true;
                }
            } else {
                if (this.$refs.FullBag) {
                    this.$refs.FullBag.ssccFlag = false;
                }
                if (this.$refs.PartialBag) {
                    this.$refs.PartialBag.ssccFlag = false;
                }
                if (this.$refs.FullAmount) {
                    this.$refs.FullAmount.ssccFlag = false;
                }
            }
        },
        EmptySscc() {
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.sscc = '';
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.sscc = '';
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.sscc = '';
            }
        },
        ChangeMaterial(num) {
            if (this.way == 'Batch' && !this.OnlyFullAmount) {
                this.activeName = 'FullBag';
            }
            if (this.way == 'Material') {
                this.activeName = 'PartialBag';
            }
            if (this.OnlyFullAmount) {
                this.activeName = 'FullAmount';
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.sscc = '';
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.sscc = '';
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.sscc = '';
            }
            let now = this.MaterialNow + num;
            this.detailobj = this.MaterialList[now];
            console.log(this.detailobj);
            this.MaterialNow = now;
            this.refresh();
        },
        async GetMaterialList() {
            let res;
            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {
                if (this.UseType == 'Batch') {
                    let data = {
                        ID: this.listId,
                        pageIndex: 1,
                        EquipmentId: this.room,
                        pageSize: 1000
                    };
                    // alert('aa');
                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样
                } else {
                    let ProIds = window.sessionStorage.getItem('ProIds');
                    let data = {
                        EqumentId: this.room,
                        ProId: JSON.parse(ProIds),
                        MaterialId: this.listId,
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样
                }
            } else {
                if (this.UseType == 'Batch') {
                    let data = {
                        ID: this.listId,
                        EquipmentId: this.room,
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样
                } else {
                    let ProIds = window.sessionStorage.getItem('ProIds');
                    let data = {
                        MaterialId: this.listId,
                        EqumentId: this.room,
                        ProId: JSON.parse(ProIds),
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样
                }
            }

            let response = res.response;
            this.MaterialList = response.data;
            this.MaterialList.forEach((item, index) => {
                if (item.OnlyId == this.detailobj.OnlyId) {
                    this.MaterialNow = index;
                }
            });
            this.detailobj = this.MaterialList[this.MaterialNow];
            this.detailobj.isGUnit = false;
            if (this.detailobj.ChangeUnit) {
                if (this.detailobj.ChangeUnit == 'g') {
                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;
                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;
                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;
                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;
                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;
                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;
                    this.detailobj.isGUnit = true;
                }
            }
            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.getDetailobj();
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.getInQuantity();
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;
                this.$refs.FullBag.Bags = 0;
            }
            if (this.$refs.BatchPallets) {
                this.$refs.BatchPallets.getTabelData();
                this.$refs.BatchPallets.getBatchPalletsStatus();
            }
        },
        async getRowBySscc(val) {
            let params = {
                MCode: this.detailobj.MCode,
                SSCC: val
            };
            let res = await MygetSSCC(params);
            if (res.response.data == null) {
                Message({
                    message: `该追溯码不存在`,
                    type: 'error'
                });
            } else {
                let data = res.response.data[0];
                if (data.Remark == 'ky') {
                    this.$refs.TopTabel.tableData.forEach(item => {
                        if (item.ID == data.ID) {
                            this.$refs.TopTabel.setCurrentRow(item);
                            this.GetCurrentRow(item);
                        }
                    });
                } else {
                    this.$refs.TopBatchTabel.tableData.forEach(item => {
                        if (item.ID == data.ID) {
                            this.$refs.TopBatchTabel.setCurrentRow(item);
                            this.GetCurrentRow2(item);
                        }
                    });
                }
            }
        },
        GetCurrentRow(val) {
            console.log(val, 2);
            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);
            // this.$refs.TopTabel.setCurrentRow();
            // this.$refs.TopBatchTabel.setCurrentRow();
            if (this.$refs.TopBatchTabel) {
                this.$refs.TopBatchTabel.setCurrentRow();
            }
            this.OnlyFullAmount = false;
            this.nowChooseRow = val;
            if (this.$refs.BatchPallets) {
                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.ssccFlag = true;
                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;
                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;
                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;
            }
            //判断整袋转移
            if (this.$refs.FullBag) {
                let InQuantity = this.nowChooseRow.InQuantity;
                if (this.detailobj.ChangeUnit) {
                    if (this.detailobj.ChangeUnit == 'g') {
                        InQuantity = InQuantity * 1000;
                    }
                }
                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;
                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];
                let key = num * this.detailobj.BagSize;
                if (num == 0) {
                    //左右相等就禁止转移，并且包数为0
                    this.$refs.FullBag.ssccFlag = false;
                    this.$refs.FullBag.Bags = 0;
                } else {
                    //不相等就判断选中数量跟差值乘以单包数量
                    this.$refs.FullBag.ssccFlag = true;
                    if (InQuantity >= key) {
                        this.$refs.FullBag.Bags = Math.floor(num);
                    } else {
                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);
                    }
                }

                this.$refs.FullBag.InQuantity = InQuantity;
                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;
                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.ssccFlag = true;
                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;
                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;
                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;
                this.$refs.FullAmount.getInQuantity();
            }
        },
        GetCurrentRow2(val) {
            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);
            this.$refs.TopTabel.setCurrentRow();
            this.activeName = 'FullAmount';
            this.OnlyFullAmount = true;
            // this.$refs.TopBatchTabel.setCurrentRow();
            this.nowChooseRow = val;
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.ssccFlag = true;
                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;
                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;
                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);
            }
        },
        async GetAddPallet() {
            let params = {
                TareWeight: this.detailobj.MQuantityTotal,
                UomID: this.detailobj.TUintid,
                ProBatchID: this.detailobj.BatchId,
                EquipMentID: this.room,
                MaterialId: this.detailobj.MaterialId,
                ProRequestID: this.detailobj.ProductionOrderId
            };
            let res = await FirstAddPallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.refresh();
        },
        refresh() {
            this.getTopData();
            this.GetMaterialList();
            if (this.way == 'Batch') {
                this.getPOTabelData();
                this.getSelectList();
            } else {
                this.$refs.POInventory.getTabelData();
            }

            // 🎯 数据刷新后重新计算表格高度
            this.$nextTick(() => {
                this.calculateTableHeight();
            });
        },
        async getSelectList() {
            let params = {
                proOrderID: this.detailobj.ProductionOrderId,
                batchID: this.detailobj.BatchId
            };
            let res = await GetConSelectList(params);
            console.log(res, 123123);
            this.SelectList = res.response;
            if (this.way == 'Batch') {
                if (this.SelectList.length != 0) {
                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);
                    if (this.$refs.BatchPallets) {
                        this.$refs.BatchPallets.BatchPalletsOption = res.response;
                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;
                    }
                }
            } else {
                this.$refs.POInventory.getTabelData();
            }
        },
        async getTopData() {
            let res;
            let params = {
                MaterialId: this.detailobj.MaterialId,
                EquipmentId: this.room,
                pageIndex: 1,
                pageSize: 1000
            };
            if (this.way == 'Batch') {
                res = await GetPageListMaterialPreTop(params);
            } else {
                res = await GetPageListNewMaterialPreTop(params);
            }
            this.tableList = res.response.data;

            // 🎯 数据加载完成后重新计算表格高度
            this.$nextTick(() => {
                this.calculateTableHeight();
            });
        },
        back(val) {
            this.$router.go(-1);
        },
        isDateInThePast(dateString) {
            const givenDate = new Date(dateString);
            const now = new Date();
            return givenDate < now;
        }
    }
};
</script>
<style lang="scss" scoped>
.buildpalletsStart {
    .InventorySearchBox {
        margin-bottom: 0px;
    }
    .tablebox {
        margin-top: 10px;
    }
    .tableboxheightall {
        overflow-y: auto;
        max-height: 87%;
    }
    .searchtipbox {
        margin: 0 5px;
        background: #90ffa2;
        height: 30px;
        padding: 0 2vh;
        display: flex;
        margin-bottom: 0.5vh;
        align-items: center;
        justify-content: center;
        color: black;
        font-size: 16px;
    }

    .expandbox {
        background: #f5f5f5;
        padding: 10px;
    }
    .el-tabs--border-card {
        border: 0;
        box-shadow: none;
    }

    // 🎯 可用库存表格自适应高度样式
    .adaptive-inventory-container {
        min-height: 200px;
        max-height: 550px;
        height: auto !important;
        transition: height 0.3s ease;

        // 自适应表格样式
        .adaptive-table {
            transition: height 0.3s ease !important;
            width: 100% !important;

            // 强制设置表格高度
            &.el-table {
                height: var(--table-height, 220px) !important;
                max-height: var(--table-height, 220px) !important;
                min-height: var(--table-height, 220px) !important;
            }

            // 表格内容区域自适应
            .el-table__body-wrapper {
                max-height: calc(var(--table-height, 220px) - 40px) !important;
                overflow-y: auto !important;

                // 美化滚动条
                &::-webkit-scrollbar {
                    width: 6px;
                }

                &::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }

                &::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;

                    &:hover {
                        background: #a8a8a8;
                    }
                }
            }

            // 表头固定高度
            .el-table__header-wrapper {
                height: 40px !important;
                min-height: 40px !important;
                max-height: 40px !important;
            }

            // 固定列样式
            .el-table__fixed,
            .el-table__fixed-right {
                height: var(--table-height, 220px) !important;
            }

            // 行数模式特殊样式
            &.height-mode-rows {
                .el-table__body tr {
                    transition: height 0.2s ease;
                }
            }

            // 视口模式特殊样式
            &.height-mode-viewport {
                .el-table__body-wrapper {
                    overflow-y: auto !important;
                }
            }
        }

        // 响应式设计
        @media (max-height: 768px) {
            max-height: 350px;
        }

        @media (max-height: 600px) {
            max-height: 280px;
        }
    }
}
</style>

{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=style&index=0&id=6d6f7003&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749634526901}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <!-- 统一备料统计信息区域 -->\r\n            <div class=\"searchbox\">\r\n                <!-- 第一行：整数袋和零数袋统计 -->\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: preparationStats.fullBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ preparationStats.fullBagCompleted }}/{{ preparationStats.fullBagTotal }}袋\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.partialBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.PartialBags') }}：{{ preparationStats.partialBagCompleted.toFixed(3) }}/{{ preparationStats.partialBagTotal.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第二行：总体备料统计 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalRequired') }}：{{ preparationStats.totalRequired.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalCompleted') }}：{{ preparationStats.totalCompleted.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第三行：理论值、最小值、最大值 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Theoretical') }}：{{ preparationStats.theoreticalValue.toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox adaptive-inventory-container\" :style=\"{\r\n                '--table-height': tableHeight + 'px',\r\n                '--dynamic-table-height': tableHeight + 'px'\r\n            }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                ({{ tableList.length }}行 | {{ tableHeight }}px | {{ useViewportHeight ? '视口模式' : '行数模式' }})\r\n                            </span> -->\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px; align-items: center;\">\r\n                            <!-- 高度模式切换按钮 -->\r\n                            <el-button\r\n                                size=\"mini\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\"\r\n                                @click=\"toggleHeightMode()\"\r\n                                :title=\"useViewportHeight ? '当前：视口模式，点击切换到行数模式' : '当前：行数模式，点击切换到视口模式'\"\r\n                                style=\"margin-right: 5px;\">\r\n                                {{ useViewportHeight ? '视口' : '行数' }}\r\n                            </el-button>\r\n\r\n                            <!-- 打印按钮 -->\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 自适应高度表格 -->\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :class=\"['adaptive-table', `height-mode-${useViewportHeight ? 'viewport' : 'rows'}`]\"\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px',\r\n                        maxHeight: tableHeight + 'px',\r\n                        minHeight: tableHeight + 'px'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableRenderKey\"\r\n                    size=\"small\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.MaterialSplit')\" name=\"MaterialSplit\">\r\n                        <MaterialSplit ref=\"MaterialSplit\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></MaterialSplit>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets'),\r\n        MaterialSplit: () => import('./components/MaterialSplit')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n\r\n            // 🎯 备料统计数据\r\n            preparationStats: {\r\n                fullBagCompleted: 0,        // 整数袋已完成数量\r\n                fullBagTotal: 0,            // 整数袋理论数量\r\n                fullBagComplete: false,     // 整数袋是否完成\r\n                partialBagCompleted: 0,     // 零数袋已完成重量\r\n                partialBagTotal: 0,         // 零数袋理论重量\r\n                partialBagComplete: false,  // 零数袋是否完成\r\n                totalRequired: 0,           // 总需求重量\r\n                totalCompleted: 0,          // 总已完成重量\r\n                totalComplete: false,       // 总体是否完成\r\n                theoreticalValue: 0         // 理论值\r\n            },\r\n\r\n            // 🎯 表格自适应高度配置\r\n            minTableHeight: 180,        // 最小高度（表头+至少2行）\r\n            maxTableHeight: 500,        // 最大高度\r\n            baseRowHeight: 42,          // 基础行高\r\n            actualRowHeight: 42,        // 实际检测到的行高\r\n            headerHeight: 40,           // 表头高度\r\n            windowHeight: window.innerHeight,\r\n            useViewportHeight: false,   // 默认使用行数模式\r\n            tableHeight: 220,           // 当前表格高度\r\n            tableRenderKey: 0,          // 强制重新渲染\r\n            isCalculating: false,       // 防止重复计算\r\n            resizeTimer: null           // 防抖定时器\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 🎯 初始化表格自适应高度功能\r\n        this.initAdaptiveHeight();\r\n\r\n        // 🎯 初始化统计数据\r\n        this.$nextTick(() => {\r\n            this.calculatePreparationStats();\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n        if (this.resizeTimer) {\r\n            clearTimeout(this.resizeTimer);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n\r\n        // 🎯 表格自适应高度核心方法\r\n        initAdaptiveHeight() {\r\n            console.log('=== 初始化表格自适应高度 ===');\r\n\r\n            // 添加窗口大小变化监听器\r\n            this.handleResize = () => {\r\n                if (this.resizeTimer) {\r\n                    clearTimeout(this.resizeTimer);\r\n                }\r\n                this.resizeTimer = setTimeout(() => {\r\n                    this.windowHeight = window.innerHeight;\r\n                    if (this.useViewportHeight) {\r\n                        this.calculateTableHeight();\r\n                    }\r\n                }, 300);\r\n            };\r\n            window.addEventListener('resize', this.handleResize);\r\n\r\n            // 初始化表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n\r\n        // 计算表格高度\r\n        calculateTableHeight() {\r\n            if (this.isCalculating) return;\r\n            this.isCalculating = true;\r\n\r\n            console.log('=== 计算表格高度 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('数据行数:', this.tableList ? this.tableList.length : 0);\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('无数据，使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 视口模式：基于窗口高度的25%\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 行数模式：基于数据行数智能计算\r\n                newHeight = this.calculateRowBasedHeight();\r\n            }\r\n\r\n            // 更新高度\r\n            if (Math.abs(this.tableHeight - newHeight) > 2) {\r\n                this.tableHeight = newHeight;\r\n                this.tableRenderKey++;\r\n                console.log('高度已更新:', this.tableHeight, 'px');\r\n\r\n                this.$nextTick(() => {\r\n                    this.applyTableHeight();\r\n                });\r\n            }\r\n\r\n            this.isCalculating = false;\r\n        },\r\n\r\n        // 基于行数计算高度\r\n        calculateRowBasedHeight() {\r\n            const padding = 8;\r\n            const borderHeight = 2;\r\n\r\n            // 检测实际行高\r\n            this.detectActualRowHeight();\r\n\r\n            // 计算总高度：表头 + 数据行 + 边距\r\n            const totalHeight = this.headerHeight +\r\n                               (this.tableList.length * this.actualRowHeight) +\r\n                               padding +\r\n                               borderHeight;\r\n\r\n            const finalHeight = Math.min(Math.max(totalHeight, this.minTableHeight), this.maxTableHeight);\r\n\r\n            console.log('行数模式计算:', {\r\n                headerHeight: this.headerHeight,\r\n                dataRows: this.tableList.length,\r\n                actualRowHeight: this.actualRowHeight,\r\n                totalHeight,\r\n                finalHeight\r\n            });\r\n\r\n            return finalHeight;\r\n        },\r\n\r\n        // 检测实际行高\r\n        detectActualRowHeight() {\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                this.$nextTick(() => {\r\n                    const tableEl = this.$refs.TopTabel.$el;\r\n                    const firstRow = tableEl.querySelector('.el-table__body tr');\r\n                    if (firstRow) {\r\n                        const detectedHeight = firstRow.offsetHeight;\r\n                        if (detectedHeight > 0 && Math.abs(detectedHeight - this.actualRowHeight) > 2) {\r\n                            console.log('检测到新的行高:', detectedHeight, '(原:', this.actualRowHeight, ')');\r\n                            this.actualRowHeight = detectedHeight;\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        },\r\n\r\n        // 应用表格高度到DOM\r\n        applyTableHeight() {\r\n            if (this.$refs.TopTabel) {\r\n                const tableComponent = this.$refs.TopTabel;\r\n                const tableEl = tableComponent.$el;\r\n\r\n                // 设置表格高度\r\n                tableEl.style.height = this.tableHeight + 'px';\r\n                tableEl.style.maxHeight = this.tableHeight + 'px';\r\n                tableEl.style.minHeight = this.tableHeight + 'px';\r\n\r\n                // 设置表格内容区域高度\r\n                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                if (bodyWrapper) {\r\n                    bodyWrapper.style.maxHeight = (this.tableHeight - this.headerHeight) + 'px';\r\n                }\r\n\r\n                // 调用Element UI的布局方法\r\n                if (tableComponent.doLayout) {\r\n                    tableComponent.doLayout();\r\n                }\r\n\r\n                console.log('DOM高度已应用:', this.tableHeight + 'px');\r\n            }\r\n        },\r\n\r\n        // 切换高度模式\r\n        toggleHeightMode() {\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n            console.log('切换到:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n\r\n            this.calculateTableHeight();\r\n        },\r\n\r\n        // 🎯 计算备料统计数据\r\n        calculatePreparationStats() {\r\n            if (!this.detailobj) return;\r\n\r\n            // 计算整数袋统计\r\n            if (this.detailobj.FullPage) {\r\n                const fullPageParts = this.detailobj.FullPage.split('/');\r\n                this.preparationStats.fullBagCompleted = parseInt(fullPageParts[0]) || 0;\r\n                this.preparationStats.fullBagTotal = parseInt(fullPageParts[1]) || 0;\r\n                this.preparationStats.fullBagComplete = this.preparationStats.fullBagCompleted >= this.preparationStats.fullBagTotal;\r\n            }\r\n\r\n            // 计算零数袋统计\r\n            this.preparationStats.partialBagCompleted = Number(this.detailobj.TagpS) || 0;\r\n            this.preparationStats.partialBagTotal = Number(this.detailobj.ParitialPage) || 0;\r\n            this.preparationStats.partialBagComplete = this.preparationStats.partialBagCompleted >= this.preparationStats.partialBagTotal;\r\n\r\n            // 计算总体统计\r\n            this.preparationStats.totalRequired = Number(this.detailobj.MQuantityTotal) || 0;\r\n            this.preparationStats.totalCompleted = Number(this.detailobj.MQuantity) || 0;\r\n            this.preparationStats.totalComplete = this.preparationStats.totalCompleted >= this.preparationStats.totalRequired;\r\n\r\n            // 计算理论值\r\n            this.preparationStats.theoreticalValue = Number(this.detailobj.MQuantityTotal) || 0;\r\n\r\n            console.log('备料统计数据已更新:', this.preparationStats);\r\n        },\r\n\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n\r\n            // 🎯 计算备料统计数据\r\n            this.calculatePreparationStats();\r\n\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n\r\n                // 🎯 如果是分包Tab，将选中的物料信息传递给MaterialSplit组件\r\n                if (this.activeName === 'MaterialSplit' && this.$refs.MaterialSplit) {\r\n                    this.$refs.MaterialSplit.setSelectedMaterial(data);\r\n                }\r\n\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n\r\n            // 🎯 数据刷新后重新计算表格高度和统计数据\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n                this.calculatePreparationStats();\r\n            });\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n\r\n            // 🎯 数据加载完成后重新计算表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 🎯 可用库存表格自适应高度样式\r\n    .adaptive-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 550px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 自适应表格样式\r\n        .adaptive-table {\r\n            transition: height 0.3s ease !important;\r\n            width: 100% !important;\r\n\r\n            // 强制设置表格高度\r\n            &.el-table {\r\n                height: var(--table-height, 220px) !important;\r\n                max-height: var(--table-height, 220px) !important;\r\n                min-height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 表格内容区域自适应\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 220px) - 40px) !important;\r\n                overflow-y: auto !important;\r\n\r\n                // 美化滚动条\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px !important;\r\n                min-height: 40px !important;\r\n                max-height: 40px !important;\r\n            }\r\n\r\n            // 固定列样式\r\n            .el-table__fixed,\r\n            .el-table__fixed-right {\r\n                height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 行数模式特殊样式\r\n            &.height-mode-rows {\r\n                .el-table__body tr {\r\n                    transition: height 0.2s ease;\r\n                }\r\n            }\r\n\r\n            // 视口模式特殊样式\r\n            &.height-mode-viewport {\r\n                .el-table__body-wrapper {\r\n                    overflow-y: auto !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 350px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 280px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749634526901}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA8OA;AACA;AACA;AACA,SACAA,6BADA,EAEAC,qBAFA,EAGAC,yBAHA,EAIAC,uBAJA,EAKAC,uBALA,EAMAC,yBANA,EAOAC,4BAPA,EAQAC,gBARA,EASAC,cATA,EAUAC,qBAVA,EAWAC,SAXA,QAYA,wCAZA;AAaA;AACA;AAEA;EACAC;IACAC,mDADA;IAEAC,mDAFA;IAGAC,6CAHA;IAIAC,qDAJA;IAKAC,uDALA;IAMAC;EANA,CADA;;EASAC;IACA;MACAC,WADA;MAEAC,iBAFA;MAGAC,iBAHA;MAIAC,uBAJA;MAKAC,mBALA;MAMAC,wBANA;MAOAC,qBAPA;MAQAC,oBARA;MASAC,aATA;MAUAC,QAVA;MAWAC,aAXA;MAYAC,oBAZA;MAaAC,cAbA;MAcAC,uCAdA;MAeAC,gCAfA;MAgBAC,OAhBA;MAiBAC,UAjBA;MAkBAC,gBAlBA;MAmBAC,gBAnBA;MAoBAC,iBApBA;MAqBAC,WArBA;MAsBAC,eAtBA;MAuBAC,kBAvBA;MAyBA;MACAC;QACAC,mBADA;QACA;QACAC,eAFA;QAEA;QACAC,sBAHA;QAGA;QACAC,sBAJA;QAIA;QACAC,kBALA;QAKA;QACAC,yBANA;QAMA;QACAC,gBAPA;QAOA;QACAC,iBARA;QAQA;QACAC,oBATA;QASA;QACAC,mBAVA,CAUA;;MAVA,CA1BA;MAuCA;MACAC,mBAxCA;MAwCA;MACAC,mBAzCA;MAyCA;MACAC,iBA1CA;MA0CA;MACAC,mBA3CA;MA2CA;MACAC,gBA5CA;MA4CA;MACAC,gCA7CA;MA8CAC,wBA9CA;MA8CA;MACAC,gBA/CA;MA+CA;MACAC,iBAhDA;MAgDA;MACAC,oBAjDA;MAiDA;MACAC,iBAlDA,CAkDA;;IAlDA;EAoDA,CA9DA;;EA+DAC;IACAC;IACAA;IACA;;IACA;MACA;IACA,CAFA,MAEA;MACA;IACA;;IACA;IACAA;IACA;IACAA;IACA;IACA;IACA;IACA,oBAhBA,CAiBA;;IACA;IACA;IACA;;IACA;MACA;MACA;IACA,CAHA,MAGA;MACA;IACA,CA1BA,CA4BA;;;IACA,0BA7BA,CA+BA;;IACA;MACA;IACA,CAFA;EAGA,CAlGA;;EAmGAC;IACAC;EACA,CArGA;;EAsGAC;IACA;IACAD;;IACA;MACAA;IACA;;IACA;MACAE;IACA;EACA,CA/GA;;EAgHAC;IACAC;MACAN;;MACA;QACAE;QACA;MACA,CAHA,MAGA;QACAA;QACA;MACA;IACA,CAVA;;IAWAK;MACA;QACA;QACAP;;QACA;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAQ;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAF;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACAV;;cACA;gBACA;cACA,CAFA,MAEA;gBACAQ;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAV;;cACA;gBACA;cACA,CAFA,MAEA;gBACAQ;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAV;;cACA;gBACA;cACA,CAFA,MAEA;gBACAQ;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;QAvDA;;QAyDA;MACA;IACA,CA1EA;;IA2EA;MACA;QACA;MACA,CAFA,EADA,CAKA;MACA;MACA;MACA;MACA;;MAEA;QACAC,qBADA;QAEAC,sBAFA;QAGAC,+BAHA;QAIAC,2BAJA;QAKAC;MALA;MAQA;MACAP;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CApGA;;IAsGA;IACAM;MACAhB,kCADA,CAGA;;MACA;QACA;UACAI;QACA;;QACA;UACA;;UACA;YACA;UACA;QACA,CALA,EAKA,GALA;MAMA,CAVA;;MAWAF,qDAfA,CAiBA;;MACA;QACA;MACA,CAFA;IAGA,CA5HA;;IA8HA;IACAe;MACA;MACA;MAEAjB;MACAA;MACAA;MAEA;;MAEA;QACAkB;QACAlB;MACA,CAHA,MAGA;QACA;QACA;QACAkB;QACAlB;UACAP,+BADA;UAEA0B,eAFA;UAGAD;QAHA;MAKA,CATA,MASA;QACA;QACAA;MACA,CAzBA,CA2BA;;;MACA;QACA;QACA;QACAlB;QAEA;UACA;QACA,CAFA;MAGA;;MAEA;IACA,CAtKA;;IAwKA;IACAoB;MACA;MACA,uBAFA,CAIA;;MACA,6BALA,CAOA;;MACA,wCACA,4CADA,GAEAC,OAFA,GAGAC,YAHA;MAKA;MAEAtB;QACAR,+BADA;QAEA+B,+BAFA;QAGAhC,qCAHA;QAIAiC,WAJA;QAKAC;MALA;MAQA;IACA,CAjMA;;IAmMA;IACAC;MACA;QACA;UACA;UACA;;UACA;YACA;;YACA;cACA1B;cACA;YACA;UACA;QACA,CAVA;MAWA;IACA,CAlNA;;IAoNA;IACA2B;MACA;QACA;QACA,mCAFA,CAIA;;QACAC;QACAA;QACAA,kDAPA,CASA;;QACA;;QACA;UACAC;QACA,CAbA,CAeA;;;QACA;UACAC;QACA;;QAEA9B;MACA;IACA,CA5OA;;IA8OA;IACA+B;MACA;MACA/B;MAEAQ;QACAC,8DADA;QAEAC,eAFA;QAGAsB;MAHA;MAMA;IACA,CA1PA;;IA4PA;IACAC;MACA,4BADA,CAGA;;MACA;QACA;QACA;QACA;QACA;MACA,CATA,CAWA;;;MACA;MACA;MACA,8HAdA,CAgBA;;MACA;MACA;MACA,kHAnBA,CAqBA;;MACA;MAEAjC;IACA,CAtRA;;IAwRA;MACA;QACAkC;MADA;MAGA;MACAC;QACAC;QACAA;QACAA;QACAA;MACA,CALA;MAMA;;MACA;QACA;MACA;IACA,CAvSA;;IAwSAC;MACA;MACA;IACA,CA3SA;;IA4SA;MACA;QACAC,+BADA;QAEAC,4CAFA;QAGAC,qCAHA;QAIA5B,sBAJA;QAKA6B,YALA;QAMAC;MANA;MAQA;MACA;IACA,CAvTA;;IAwTAC;MACA;QACA;MACA,CAFA;;MAGA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA,CAVA,MAUA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA;IACA,CAjVA;;IAkVAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;IACA,CA5VA;;IA6VAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;MACA;MACA7C;MACA;MACA;IACA,CArXA;;IAsXA;MACA;;MACA;QACA;UACA;YACA8C,eADA;YAEAL,YAFA;YAGA7B,sBAHA;YAIA8B;UAJA,EADA,CAOA;;UACAK,wCARA,CAQA;QACA,CATA,MASA;UACA;UACA;YACAC,oBADA;YAEAC,yBAFA;YAGAT,uBAHA;YAIAC,YAJA;YAKAC;UALA;UAOAK,wCATA,CASA;QACA;MACA,CArBA,MAqBA;QACA;UACA;YACAD,eADA;YAEAlC,sBAFA;YAGA6B,YAHA;YAIAC;UAJA;UAMAK,0CAPA,CAOA;QACA,CARA,MAQA;UACA;UACA;YACAP,uBADA;YAEAQ,oBAFA;YAGAC,yBAHA;YAIAR,YAJA;YAKAC;UALA;UAOAK,0CATA,CASA;QACA;MACA;;MAEA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;MAKA;MACA;;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;;MACA,qJAjEA,CAmEA;;MACA;;MAEA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;MACA;;MACA;QACA;QACA;MACA;IACA,CA1cA;;IA2cA;MACA;QACAjC,2BADA;QAEAoC;MAFA;MAIA;;MACA;QACA1C;UACAC,kBADA;UAEAC;QAFA;MAIA,CALA,MAKA;QACA,gCADA,CAGA;;QACA;UACA;QACA;;QAEA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA,CAPA,MAOA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA;MACA;IACA,CA9eA;;IA+eAyC;MACAnD;MACA,iEAFA,CAGA;MACA;;MACA;QACA;MACA;;MACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;MACA,CAlBA,CAmBA;;;MACA;QACA;;QACA;UACA;YACAoD;UACA;QACA;;QACA;QACA;QACA;;QACA;UACA;UACA;UACA;QACA,CAJA,MAIA;UACA;UACA;;UACA;YACA;UACA,CAFA,MAEA;YACA;UACA;QACA;;QAEA;QACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA,CAtiBA;;IAuiBAC;MACA;MACA;MACA;MACA,2BAJA,CAKA;;MACA;;MACA;QACA;QACA;QACA;QACA;MACA;IACA,CApjBA;;IAqjBA;MACA;QACAC,yCADA;QAEAC,6BAFA;QAGAC,kCAHA;QAIAC,sBAJA;QAKAjB,qCALA;QAMAkB;MANA;MAQA;MACAlD;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CApkBA;;IAqkBAiD;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA,CARA,CAUA;;;MACA;QACA;QACA;MACA,CAHA;IAIA,CAplBA;;IAqlBA;MACA;QACAC,4CADA;QAEAC;MAFA;MAIA;MACA7D;MACA;;MACA;QACA;UACAE;;UACA;YACA;YACA;UACA;QACA;MACA,CARA,MAQA;QACA;MACA;IACA,CAxmBA;;IAymBA;MACA;MACA;QACAsC,qCADA;QAEA5B,sBAFA;QAGA6B,YAHA;QAIAC;MAJA;;MAMA;QACAK;MACA,CAFA,MAEA;QACAA;MACA;;MACA,mCAbA,CAeA;;MACA;QACA;MACA,CAFA;IAGA,CA5nBA;;IA6nBAe;MACA;IACA,CA/nBA;;IAgoBAC;MACA;MACA;MACA;IACA;;EApoBA;AAhHA", "names": ["GetPageListNewMaterialPreDown", "GetPageListByMaterial", "GetPageListByBatchIDSByID", "GetPageListByMaterialII", "GetPageListByBatchIDSII", "GetPageListMaterialPreTop", "GetPageListNewMaterialPreTop", "GetConSelectList", "FirstAddPallet", "GetPageListByBatchIDS", "MygetSSCC", "components", "PartialBag", "FullAmount", "FullBag", "POInventory", "BatchPallets", "MaterialSplit", "data", "PrintId", "printeroption", "PrintModel", "isExpirationDate", "tableId", "activeName", "OnlyFullAmount", "Hidecompleted", "<PERSON><PERSON><PERSON>", "room", "tableList", "tableListBatchPO", "SelectList", "headerBatchPO", "header", "way", "listId", "nowChooseRow", "MaterialList", "MaterialNow", "UseType", "clblFlag", "keepKeyDown", "preparationStats", "fullBagCompleted", "fullBagTotal", "fullBagComplete", "partialBagCompleted", "partialBagTotal", "partialBagComplete", "totalRequired", "totalCompleted", "totalComplete", "theoreticalValue", "minTableHeight", "maxTableHeight", "baseRowHeight", "actualRowHeight", "headerHeight", "windowHeight", "useViewportHeight", "tableHeight", "table<PERSON><PERSON><PERSON><PERSON>", "isCalculating", "resizeTimer", "mounted", "console", "beforeMount", "window", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "openKeyDown", "getKeyDown", "Message", "message", "type", "printId", "EquipmentId", "BagSiZe", "MCode", "ids", "initAdaptiveHeight", "calculateTableHeight", "newHeight", "availableHeight", "calculateRowBasedHeight", "padding", "borderHeight", "dataRows", "totalHeight", "finalHeight", "detectActualRowHeight", "applyTableHeight", "tableEl", "bodyWrapper", "tableComponent", "toggleHeightMode", "duration", "calculatePreparationStats", "equipmentId", "res2", "item", "PrintAvallable", "BatchId", "ProOrderid", "MaterialId", "pageIndex", "pageSize", "GetSSCC", "EmptySscc", "ChangeMaterial", "ID", "res", "EqumentId", "ProId", "SSCC", "GetCurrentRow", "InQuantity", "GetCurrentRow2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UomID", "ProBatchID", "EquipMentID", "ProRequestID", "refresh", "proOrderID", "batchID", "back", "isDateInThePast"], "sourceRoot": "src/views/Inventory/buildpalletsStart", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <!-- 统一备料统计信息区域 -->\r\n            <div class=\"searchbox\">\r\n                <!-- 第一行：整数袋和零数袋统计 -->\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: preparationStats.fullBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ preparationStats.fullBagCompleted }}/{{ preparationStats.fullBagTotal }}袋\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.partialBagComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.PartialBags') }}：{{ preparationStats.partialBagCompleted.toFixed(3) }}/{{ preparationStats.partialBagTotal.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第二行：总体备料统计 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalRequired') }}：{{ preparationStats.totalRequired.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: preparationStats.totalComplete ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.TotalCompleted') }}：{{ preparationStats.totalCompleted.toFixed(3) }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n\r\n                <!-- 第三行：理论值、最小值、最大值 -->\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Theoretical') }}：{{ preparationStats.theoreticalValue.toFixed(3) }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox adaptive-inventory-container\" :style=\"{\r\n                '--table-height': tableHeight + 'px',\r\n                '--dynamic-table-height': tableHeight + 'px'\r\n            }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                ({{ tableList.length }}行 | {{ tableHeight }}px | {{ useViewportHeight ? '视口模式' : '行数模式' }})\r\n                            </span> -->\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px; align-items: center;\">\r\n                            <!-- 高度模式切换按钮 -->\r\n                            <el-button\r\n                                size=\"mini\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\"\r\n                                @click=\"toggleHeightMode()\"\r\n                                :title=\"useViewportHeight ? '当前：视口模式，点击切换到行数模式' : '当前：行数模式，点击切换到视口模式'\"\r\n                                style=\"margin-right: 5px;\">\r\n                                {{ useViewportHeight ? '视口' : '行数' }}\r\n                            </el-button>\r\n\r\n                            <!-- 打印按钮 -->\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 自适应高度表格 -->\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :class=\"['adaptive-table', `height-mode-${useViewportHeight ? 'viewport' : 'rows'}`]\"\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px',\r\n                        maxHeight: tableHeight + 'px',\r\n                        minHeight: tableHeight + 'px'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableRenderKey\"\r\n                    size=\"small\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.MaterialSplit')\" name=\"MaterialSplit\">\r\n                        <MaterialSplit ref=\"MaterialSplit\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></MaterialSplit>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets'),\r\n        MaterialSplit: () => import('./components/MaterialSplit')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n\r\n            // 🎯 备料统计数据\r\n            preparationStats: {\r\n                fullBagCompleted: 0,        // 整数袋已完成数量\r\n                fullBagTotal: 0,            // 整数袋理论数量\r\n                fullBagComplete: false,     // 整数袋是否完成\r\n                partialBagCompleted: 0,     // 零数袋已完成重量\r\n                partialBagTotal: 0,         // 零数袋理论重量\r\n                partialBagComplete: false,  // 零数袋是否完成\r\n                totalRequired: 0,           // 总需求重量\r\n                totalCompleted: 0,          // 总已完成重量\r\n                totalComplete: false,       // 总体是否完成\r\n                theoreticalValue: 0         // 理论值\r\n            },\r\n\r\n            // 🎯 表格自适应高度配置\r\n            minTableHeight: 180,        // 最小高度（表头+至少2行）\r\n            maxTableHeight: 500,        // 最大高度\r\n            baseRowHeight: 42,          // 基础行高\r\n            actualRowHeight: 42,        // 实际检测到的行高\r\n            headerHeight: 40,           // 表头高度\r\n            windowHeight: window.innerHeight,\r\n            useViewportHeight: false,   // 默认使用行数模式\r\n            tableHeight: 220,           // 当前表格高度\r\n            tableRenderKey: 0,          // 强制重新渲染\r\n            isCalculating: false,       // 防止重复计算\r\n            resizeTimer: null           // 防抖定时器\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 🎯 初始化表格自适应高度功能\r\n        this.initAdaptiveHeight();\r\n\r\n        // 🎯 初始化统计数据\r\n        this.$nextTick(() => {\r\n            this.calculatePreparationStats();\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n        if (this.resizeTimer) {\r\n            clearTimeout(this.resizeTimer);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n\r\n        // 🎯 表格自适应高度核心方法\r\n        initAdaptiveHeight() {\r\n            console.log('=== 初始化表格自适应高度 ===');\r\n\r\n            // 添加窗口大小变化监听器\r\n            this.handleResize = () => {\r\n                if (this.resizeTimer) {\r\n                    clearTimeout(this.resizeTimer);\r\n                }\r\n                this.resizeTimer = setTimeout(() => {\r\n                    this.windowHeight = window.innerHeight;\r\n                    if (this.useViewportHeight) {\r\n                        this.calculateTableHeight();\r\n                    }\r\n                }, 300);\r\n            };\r\n            window.addEventListener('resize', this.handleResize);\r\n\r\n            // 初始化表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n\r\n        // 计算表格高度\r\n        calculateTableHeight() {\r\n            if (this.isCalculating) return;\r\n            this.isCalculating = true;\r\n\r\n            console.log('=== 计算表格高度 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('数据行数:', this.tableList ? this.tableList.length : 0);\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('无数据，使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 视口模式：基于窗口高度的25%\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 行数模式：基于数据行数智能计算\r\n                newHeight = this.calculateRowBasedHeight();\r\n            }\r\n\r\n            // 更新高度\r\n            if (Math.abs(this.tableHeight - newHeight) > 2) {\r\n                this.tableHeight = newHeight;\r\n                this.tableRenderKey++;\r\n                console.log('高度已更新:', this.tableHeight, 'px');\r\n\r\n                this.$nextTick(() => {\r\n                    this.applyTableHeight();\r\n                });\r\n            }\r\n\r\n            this.isCalculating = false;\r\n        },\r\n\r\n        // 基于行数计算高度\r\n        calculateRowBasedHeight() {\r\n            const padding = 8;\r\n            const borderHeight = 2;\r\n\r\n            // 检测实际行高\r\n            this.detectActualRowHeight();\r\n\r\n            // 计算总高度：表头 + 数据行 + 边距\r\n            const totalHeight = this.headerHeight +\r\n                               (this.tableList.length * this.actualRowHeight) +\r\n                               padding +\r\n                               borderHeight;\r\n\r\n            const finalHeight = Math.min(Math.max(totalHeight, this.minTableHeight), this.maxTableHeight);\r\n\r\n            console.log('行数模式计算:', {\r\n                headerHeight: this.headerHeight,\r\n                dataRows: this.tableList.length,\r\n                actualRowHeight: this.actualRowHeight,\r\n                totalHeight,\r\n                finalHeight\r\n            });\r\n\r\n            return finalHeight;\r\n        },\r\n\r\n        // 检测实际行高\r\n        detectActualRowHeight() {\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                this.$nextTick(() => {\r\n                    const tableEl = this.$refs.TopTabel.$el;\r\n                    const firstRow = tableEl.querySelector('.el-table__body tr');\r\n                    if (firstRow) {\r\n                        const detectedHeight = firstRow.offsetHeight;\r\n                        if (detectedHeight > 0 && Math.abs(detectedHeight - this.actualRowHeight) > 2) {\r\n                            console.log('检测到新的行高:', detectedHeight, '(原:', this.actualRowHeight, ')');\r\n                            this.actualRowHeight = detectedHeight;\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        },\r\n\r\n        // 应用表格高度到DOM\r\n        applyTableHeight() {\r\n            if (this.$refs.TopTabel) {\r\n                const tableComponent = this.$refs.TopTabel;\r\n                const tableEl = tableComponent.$el;\r\n\r\n                // 设置表格高度\r\n                tableEl.style.height = this.tableHeight + 'px';\r\n                tableEl.style.maxHeight = this.tableHeight + 'px';\r\n                tableEl.style.minHeight = this.tableHeight + 'px';\r\n\r\n                // 设置表格内容区域高度\r\n                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                if (bodyWrapper) {\r\n                    bodyWrapper.style.maxHeight = (this.tableHeight - this.headerHeight) + 'px';\r\n                }\r\n\r\n                // 调用Element UI的布局方法\r\n                if (tableComponent.doLayout) {\r\n                    tableComponent.doLayout();\r\n                }\r\n\r\n                console.log('DOM高度已应用:', this.tableHeight + 'px');\r\n            }\r\n        },\r\n\r\n        // 切换高度模式\r\n        toggleHeightMode() {\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n            console.log('切换到:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n\r\n            this.calculateTableHeight();\r\n        },\r\n\r\n        // 🎯 计算备料统计数据\r\n        calculatePreparationStats() {\r\n            if (!this.detailobj) return;\r\n\r\n            // 计算整数袋统计\r\n            if (this.detailobj.FullPage) {\r\n                const fullPageParts = this.detailobj.FullPage.split('/');\r\n                this.preparationStats.fullBagCompleted = parseInt(fullPageParts[0]) || 0;\r\n                this.preparationStats.fullBagTotal = parseInt(fullPageParts[1]) || 0;\r\n                this.preparationStats.fullBagComplete = this.preparationStats.fullBagCompleted >= this.preparationStats.fullBagTotal;\r\n            }\r\n\r\n            // 计算零数袋统计\r\n            this.preparationStats.partialBagCompleted = Number(this.detailobj.TagpS) || 0;\r\n            this.preparationStats.partialBagTotal = Number(this.detailobj.ParitialPage) || 0;\r\n            this.preparationStats.partialBagComplete = this.preparationStats.partialBagCompleted >= this.preparationStats.partialBagTotal;\r\n\r\n            // 计算总体统计\r\n            this.preparationStats.totalRequired = Number(this.detailobj.MQuantityTotal) || 0;\r\n            this.preparationStats.totalCompleted = Number(this.detailobj.MQuantity) || 0;\r\n            this.preparationStats.totalComplete = this.preparationStats.totalCompleted >= this.preparationStats.totalRequired;\r\n\r\n            // 计算理论值\r\n            this.preparationStats.theoreticalValue = Number(this.detailobj.MQuantityTotal) || 0;\r\n\r\n            console.log('备料统计数据已更新:', this.preparationStats);\r\n        },\r\n\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n\r\n            // 🎯 计算备料统计数据\r\n            this.calculatePreparationStats();\r\n\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n\r\n                // 🎯 如果是分包Tab，将选中的物料信息传递给MaterialSplit组件\r\n                if (this.activeName === 'MaterialSplit' && this.$refs.MaterialSplit) {\r\n                    this.$refs.MaterialSplit.setSelectedMaterial(data);\r\n                }\r\n\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n\r\n            // 🎯 数据刷新后重新计算表格高度和统计数据\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n                this.calculatePreparationStats();\r\n            });\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n\r\n            // 🎯 数据加载完成后重新计算表格高度\r\n            this.$nextTick(() => {\r\n                this.calculateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 🎯 可用库存表格自适应高度样式\r\n    .adaptive-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 550px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 自适应表格样式\r\n        .adaptive-table {\r\n            transition: height 0.3s ease !important;\r\n            width: 100% !important;\r\n\r\n            // 强制设置表格高度\r\n            &.el-table {\r\n                height: var(--table-height, 220px) !important;\r\n                max-height: var(--table-height, 220px) !important;\r\n                min-height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 表格内容区域自适应\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 220px) - 40px) !important;\r\n                overflow-y: auto !important;\r\n\r\n                // 美化滚动条\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px !important;\r\n                min-height: 40px !important;\r\n                max-height: 40px !important;\r\n            }\r\n\r\n            // 固定列样式\r\n            .el-table__fixed,\r\n            .el-table__fixed-right {\r\n                height: var(--table-height, 220px) !important;\r\n            }\r\n\r\n            // 行数模式特殊样式\r\n            &.height-mode-rows {\r\n                .el-table__body tr {\r\n                    transition: height 0.2s ease;\r\n                }\r\n            }\r\n\r\n            // 视口模式特殊样式\r\n            &.height-mode-viewport {\r\n                .el-table__body-wrapper {\r\n                    overflow-y: auto !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 350px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 280px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}
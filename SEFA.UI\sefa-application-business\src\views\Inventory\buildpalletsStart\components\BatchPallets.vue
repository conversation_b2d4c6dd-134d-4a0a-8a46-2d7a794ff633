<template>
    <div class="usemystyle buildpalletsStart">
        <div class="InventorySearchBox">
            <div class="searchbox">
                <div class="searchboxTitle" style="font-size: 14px">{{ $t('MaterialPreparationBuild.BatchPallets') }}</div>
            </div>
            <div class="searchbox">
                <div class="inputformbox longwidthinput" style="width: 250px">
                    <el-select clearable v-model="BatchPallets" :placeholder="$t('MaterialPreparationBuild.BatchPallets')" @change="BatchPalletsChange">
                        <el-option v-for="(it, ind) in BatchPalletsOption" :key="ind" :label="it.ContainerName + '-' + it.ContainerState" :value="it.ID"></el-option>
                    </el-select>
                </div>
                <el-button class="tablebtn" size="small" id="overBtn" :disabled="myContainerState" style="margin-left: 5px; width: 140px" icon="el-icon-success" @click="getCompletePallet()">
                    {{ this.$t('MaterialPreparationBuild.CompletePallet') }}
                </el-button>
                <el-button class="tablebtn" size="small" :disabled="OpenPalletState" style="margin-left: 5px; width: 140px" icon="el-icon-success" @click="getOpenPallet()">
                    {{ this.$t('MaterialPreparationBuild.OpenPallet') }}
                </el-button>
                <div class="inputformbox btnselect" style="width: 250px">
                    <el-select clearable v-model="Operate" :placeholder="$t('MaterialPreparationBuild.Operate')" @change="OperateChange">
                        <el-option v-for="item in OperateList" :key="item.value" :label="item.label" :value="item.value">
                            <i :class="item.icon"></i>
                            <span style="float: right; font-size: 13px">{{ item.label }}</span>
                        </el-option>
                    </el-select>
                </div>
                <div class="inputformbox" style="width: 200px">
                    <el-checkbox v-model="materialonly" @change="getTabelData()">{{ $t('MaterialPreparationBuild.Currentmaterialonly') }}</el-checkbox>
                </div>
                <el-button class="tablebtn" @click="ClickPrint()" size="small" style="margin-left: 5px; width: 100px; position: absolute; right: 160px" icon="el-icon-printer">
                    {{ this.$t('MaterialPreparationBuild.ReprintBtn') }}
                </el-button>
                <el-button
                    class="tablebtn"
                    :disabled="selectTabelData.length < 0"
                    size="small"
                    @click="GetRemoveBags()"
                    style="margin-left: 5px; width: 140px; position: absolute; right: 10px"
                    icon="el-icon-top"
                >
                    {{ this.$t('MaterialPreparationBuild.RemoveBags') }}
                </el-button>
            </div>
        </div>
        <el-table :data="tableList" @selection-change="handleSelectionChange" style="width: 100%" height="500">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column
                v-for="(item, index) in header"
                :fixed="item.fixed ? item.fixed : false"
                :key="index"
                :align="item.align"
                :prop="item.prop ? item.prop : item.value"
                :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                :width="item.width"
            >
                <template slot-scope="scope">
                    <span v-if="scope.column.property == 'SSCC/Batch'">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>
                    <span v-else-if="scope.column.property == 'Material'">
                        <div>{{ scope.row.MaterialCode }}</div>
                        <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                    </span>
                    <span v-else-if="scope.column.property == 'SSCCStatus'">
                        <div :class="'statusbox status' + scope.row.SbStatus">
                            {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}
                        </div>
                    </span>
                    <span v-else-if="scope.column.property == 'ExpirationDate'">
                        <div class="statusbox" :style="{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }">{{ scope.row.ExpirationDate }}</div>
                    </span>
                    <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>
                    <!-- <span v-else-if="scope.column.property == 'operate'">
                        <el-button size="mini" class="operateBtn tablebtn" @click="ClickPrint(scope.row)" icon="el-icon-printer">{{ $t('PalletList.Reprint') }}</el-button>
                    </span> -->
                    <span v-else>{{ scope.row[item.prop] }}</span>
                </template>
                <!-- <template slot-scope="scope"></template> -->
            </el-table-column>
        </el-table>
        <el-dialog :title="$t('PalletList.Reprint')" id="Printerdialog" :visible.sync="PrinterModel" width="650px">
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Printerinputlist" :key="index">
                    <div class="dialogdetailsinglelabel" style="font-weight: 500">{{ item.Required ? item.label + ' *' : item.label }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-input onkeyup="value=value.replace(/^0+|[^0-9\.]/g, '')" v-if="item.type == 'number'" v-model="item.value"></el-input>
                        <el-input v-else-if="item.type == 'input'" v-model="item.value"></el-input>
                        <el-select clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                            <el-option v-for="it in item.options" :key="it.ID" :label="it.Code" :value="it.ID"></el-option>
                        </el-select>

                        <div v-else-if="item.type == 'textArea'" style="display: flex; align-items: center">
                            <el-input disabled v-model="item.value2"></el-input>
                            <span style="margin: 0 5px">-</span>
                            <el-input onkeyup="if(isNaN(value))execCommand('undo')" onafterpaste="if(isNaN(value))execCommand('undo')" @change="getValue3" v-model="item.value3"></el-input>
                            <span style="margin: 0 5px">=</span>
                            <el-input disabled v-model="item.value"></el-input>
                            <span style="margin-left: 10px; width: 18%">{{ item.unit }}</span>
                        </div>
                        <el-select clearable v-else-if="item.type == 'trea'" v-model="item.value" filterable @change="ChangeRemark">
                            <el-option v-for="(it, ind) in RemarkList" :key="ind" :label="it.ItemName" :value="it.ItemName"></el-option>
                        </el-select>
                        <!-- <el-input v-else-if="item.type == 'trea'" type="textarea" autosize v-model="item.value"></el-input> -->
                        <el-input v-else-if="item.type == 'textarea'" type="textarea" autosize v-model="item.value"></el-input>
                        <span v-else>{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-printer" @click="getReprint()">
                    {{ $t('PalletList.Reprint') }}
                </el-button>
                <el-button @click="PrinterModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { GetPrinit7 } from '@/api/Inventory/common.js';
import { Message } from 'element-ui';
import { POBatchPalletsColumn } from '@/columns/factoryPlant/tableHeaders';
import $ from 'jquery';
import {
    GetPageListMaterialPreDown,
    CompletePallet,
    OpenPallet,
    GetConSelectList,
    FirstAddPallet,
    DeletePallet,
    RemovePallet,
    GetConsumlList,
    PalletIsFinish_PG,
    GetReprintSave
} from '@/api/Inventory/MaterialPreparation.js';

export default {
    data() {
        return {
            tableList: [],
            tableId: 'INV_CLZB',
            header: POBatchPalletsColumn,
            selectTabelData: [],
            BatchPallets: '',
            BatchPalletsOption: [],
            detailobj: {},
            materialonly: false,
            Operate: '',
            InQuantity: '',
            PrinterModel: false,
            Printerinputlist: [
                {
                    label: this.$t('MaterialPreparationBuild.zts'),
                    value: '',
                    type: 'number',
                    Required: true,
                    id: 'Totalts'
                },
                {
                    // label: this.$t('MaterialPreparationBuild.dqgxh'),
                    // value: '',
                    // Required: true,
                    // type: 'input',
                    // id: 'XHNumber'
                },
                {
                    // label: this.$t('MaterialPreparationBuild.ts'),
                    // value: '',
                    // Required: true,
                    // type: 'number',
                    // id: 'TS'
                },
                {
                    label: this.$t('MaterialPreparationBuild.jsgs'),
                    type: 'textArea',
                    Required: true,
                    value2: '',
                    value3: '',
                    value: '',
                    unit: '',
                    id: 'WNumber'
                },
                {
                    label: this.$t('Overview.CommentsOption'),
                    value: '',
                    filterable: true,
                    //    Required: true,
                    type: 'trea',
                    id: 'RemarkList'
                },
                {
                    label: this.$t('Overview.Comments'),
                    value: '',
                    //    Required: true,
                    type: 'textarea',
                    id: 'Remark'
                },
                {
                    label: this.$t('MaterialPreparationBuild.xzdyj'),
                    type: 'select',
                    value: '',
                    options: [],
                    id: 'PrintID'
                }
            ],
            OperateList: [
                {
                    value: 'add',
                    label: this.$t('MaterialPreparationBuild.Addpallets'),
                    icon: 'el-icon-plus'
                },
                {
                    value: 'delete',
                    label: this.$t('MaterialPreparationBuild.Deletepallets'),
                    icon: 'el-icon-circle-close'
                }
            ],
            myContainerState: true,
            OpenPalletState: true,
            RemarkList: []
        };
    },
    async mounted() {
        this.room = window.sessionStorage.getItem('room');
        let Remark = await this.$getNewDataDictionary('PalletQuickFields');
        this.RemarkList = Remark;
        this.getSelectList();
        this.getPrintList();
    },
    methods: {
        ChangeRemark(val) {
            this.Printerinputlist[5].value = this.Printerinputlist[5].value + val;
        },
        handleSelect(item) {
            console.log(item);
        },
        async getPrintList() {
            let params = {
                equipmentId: this.room
            };
            let res = await GetPrinit7(params);
            this.Printerinputlist[this.Printerinputlist.length - 1].options = res.response;
        },
        ClickPrint() {
            this.getValue1();
        },
        async getValue1() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                PID: this.detailobj.ProductionOrderId,
                BatchId: this.detailobj.BatchId,
                MCode: '7300030001' //水
            };
            let res = await GetConsumlList(params);
            let data = res.response;
            if (data) {
                console.log(data.data);
                if (data.dataCount == 0) {
                    this.Printerinputlist[3].value2 = 0;
                    this.Printerinputlist[3].unit = '';
                } else {
                    this.Printerinputlist[3].value2 = data.data[0].Quantity;
                    this.Printerinputlist[3].unit = data.data[0].UName;
                }
            }
            this.Printerinputlist[4].value = '';
            this.Printerinputlist[5].value = '';
            this.Printerinputlist[6].value = '';
            if (this.Printerinputlist[6].options.length != 0) {
                this.Printerinputlist[6].value = this.Printerinputlist[6].options[0].ID;
            }
            this.PrinterModel = true;
        },
        getValue3(val) {
            if (this.Printerinputlist[3].value2 !== '') {
                this.Printerinputlist[3].value = (Number(this.Printerinputlist[3].value2) - Number(this.Printerinputlist[3].value3)).toFixed(3);
            }
        },
        //打印保存
        async getReprint() {
            console.log(this.BatchPallets);
            this.detailobj = this.$parent.detailobj; //这里的this.detailobj就是工单的数据
            let flag = this.Printerinputlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (this.Printerinputlist[3].value3 == '') {
                flag = true;
            } else {
                flag = false;
            }
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let ContainerName = '';
            let containerID = '';
            this.BatchPalletsOption.forEach(item => {
                if (item.ID == this.BatchPallets) {
                    containerID = item.ID;
                    ContainerName = item.ContainerName;
                }
            });
            let addobj = {};
            this.Printerinputlist.forEach(item => {
                addobj[item.id] = item.value;
                if (item.value2) {
                    addobj.unit = item.unit; //计算公式的单位

                    addobj.WNumberText = item.value2 + '-' + item.value3 + '=' + item.value;
                }
            });
            addobj.EquipmentId = this.room;
            addobj.ContainerName = ContainerName; // 容器编号
            addobj.ContainerID = containerID;
            addobj.BatchId = this.detailobj.BatchId;
            addobj.PID = this.detailobj.ProductionOrderId; //这里是示例，获取工单ID并且给传参PID赋值
            let res = await GetReprintSave(addobj);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.PrinterModel = false;
        },
        async getCompletePallet() {
            this.detailobj = this.$parent.detailobj;
            let containerIDs = this.BatchPalletsOption.map(item => {
                return item.ID;
            });
            let params = {
                containerID: this.BatchPallets,
                containerIDs: containerIDs,
                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),
                TagWeight: this.detailobj.MQuantityTotal == null ? 0 : Number(this.detailobj.MQuantityTotal),
                UomID: this.detailobj.TUintid,
                MaterialId: this.detailobj.MaterialId,
                ProID: this.detailobj.ProductionOrderId,
                BatchID: this.detailobj.BatchId
                // ProID:'',
                // BatchID:''
            };
            let res = await CompletePallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$parent.refresh();
            this.$parent.EmptySscc();
        },
        async getOpenPallet() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                containerID: this.BatchPallets,
                MaterialId: this.detailobj.MaterialId
            };
            let res = await OpenPallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$parent.refresh();
            this.$parent.EmptySscc();
        },
        async getSelectList() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                proOrderID: this.detailobj.ProductionOrderId,
                batchID: this.detailobj.BatchId
            };
            let res = await GetConSelectList(params);
            this.BatchPalletsOption = res.response;
            this.BatchPallets = this.BatchPalletsOption[0].ID;
            this.getBatchPalletsStatus();
            this.getTabelData();
        },
        async getBatchPalletsStatus() {
            this.detailobj = this.$parent.detailobj;
            console.log(this.detailobj, 666);
            if (this.BatchPallets == '') {
                this.myContainerState = true;
                this.OpenPalletState = true;
                return;
            }

            let flag = false;
            this.BatchPalletsOption.forEach(item => {
                if (item.ID == this.BatchPallets) {
                    if (item.ContainerState == 'complete' || item.ContainerState == '已完成' || item.ContainerState == '拼锅已完成') {
                        flag = true;
                        this.myContainerState = true;
                        return false;
                    } else {
                        flag = false;
                        this.myContainerState = false;
                    }
                }
            });
            if (flag) {
                this.myContainerState = true;
                this.OpenPalletState = false;

                return false;
            } else {
                this.myContainerState = false;
                this.OpenPalletState = true;
            }
            if (this.detailobj.MQuantity >= this.detailobj.MinPvalue && this.detailobj.MQuantity <= this.detailobj.MaxPvalue) {
                this.myContainerState = false;
            } else {
                this.myContainerState = true;
            }
            let params = {
                batchID: this.detailobj.BatchId,
                eqpmentID: this.room
            };
            let res = await PalletIsFinish_PG(params);
            if (res.msg != '失败') {
                this.myContainerState = false;
                if (this.myContainerState == false) {
                    $('#overBtn').addClass('myfadeIn');
                } else {
                    $('#overBtn').removeClass('myfadeIn');
                }
            } else {
                this.myContainerState = true;
                $('#overBtn').removeClass('myfadeIn');
            }
        },
        BatchPalletsChange() {
            this.getBatchPalletsStatus();
            window.sessionStorage.setItem('BatchPallets', this.BatchPallets);
            this.getTabelData();
        },
        OperateChange(val) {
            if (val == 'add') {
                this.AddNewPallet();
            } else if (val == 'delete') {
                this.getDeletePallet();
            }
            this.Operate = '';
        },
        async GetRemoveBags() {
            this.detailobj = this.$parent.detailobj;
            // alert(this.detailobj);
            // console.log(this.detailobj);
            let subIDs = this.selectTabelData.map(item => {
                return item.SubId;
            });
            let params = {
                subIDs: subIDs,
                proOrderID: this.detailobj.ProductionOrderId,
                batchID: this.detailobj.BatchId
            };
            let res = await RemovePallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$parent.refresh();
            this.$parent.EmptySscc();
        },
        async getDeletePallet() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                ContainerId: this.BatchPallets,
                actualWeight: this.InQuantity,
                UomID: this.detailobj.TUintid
            };
            let res = await DeletePallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$parent.refresh();
            this.$parent.EmptySscc();
        },

        async AddNewPallet() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                TareWeight: this.detailobj.MQuantityTotal,
                UomID: this.detailobj.TUintid,
                ProBatchID: this.detailobj.BatchId,
                EquipMentID: this.room,
                MaterialId: this.detailobj.MaterialId,
                ProRequestID: this.detailobj.ProductionOrderId
            };
            let res = await FirstAddPallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$parent.refresh();
            this.$parent.EmptySscc();
        },
        async getTabelData() {
            this.detailobj = this.$parent.detailobj;
            let params = {
                mId: this.materialonly ? this.detailobj.MaterialId : '',
                // MaterialId: this.detailobj.MaterialId,
                ContainerId: this.BatchPallets,
                EquipmentId: this.room,
                pageIndex: 1,
                pageSize: 1000
            };
            let res = await GetPageListMaterialPreDown(params);
            this.tableList = res.response.data;
        },
        handleSelectionChange(val) {
            this.selectTabelData = val;
        },
        isDateInThePast(dateString) {
            const givenDate = new Date(dateString);
            const now = new Date();
            return givenDate < now;
        }
    }
};
</script>
<style lang="scss">
.buildpalletsStart {
    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin: 10px 0;
        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 20%;
            text-align: right;
        }

        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }
    .inputformbox {
        width: 18vh;
    }
}
#Printerdialog {
    .colInputLabel {
        margin-top: 10px;
        font-weight: 600;
    }
    .el-select {
        margin-top: 10px;
        width: 100% !important;
    }
    .el-autocomplete {
        width: 100% !important;
    }
}
.buildpalletsStart .longwidthinput .el-select {
    width: 100%;
}

.buildpalletsStart .btnselect .el-input__inner {
    // background: #3dcd58;
    ::placeholder {
        color: #fff;
    }
}
</style>

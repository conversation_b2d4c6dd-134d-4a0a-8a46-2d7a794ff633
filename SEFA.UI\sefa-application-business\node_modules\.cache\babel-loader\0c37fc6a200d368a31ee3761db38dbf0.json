{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\BatchPallets.vue", "mtime": 1749634401315}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAqHA;AACA;AACA;AACA;AACA;AACA,SACAA,0BADA,EAEAC,cAFA,EAGAC,UAHA,EAIAC,gBAJA,EAKAC,cALA,EAMAC,YANA,EAOAC,YAPA,EAQAC,cARA,EASAC,iBATA,EAUAC,cAVA,QAWA,wCAXA;AAaA;EACAC;IACA;MACAC,aADA;MAEAC,mBAFA;MAGAC,4BAHA;MAIAC,mBAJA;MAKAC,gBALA;MAMAC,sBANA;MAOAC,aAPA;MAQAC,mBARA;MASAC,WATA;MAUAC,cAVA;MAWAC,mBAXA;MAYAC,mBACA;QACAC,8CADA;QAEAC,SAFA;QAGAC,cAHA;QAIAC,cAJA;QAKAC;MALA,CADA,EAQA,CACA;QACA;QACA;QACA;QACA;MALA,CARA,EAeA,CACA;QACA;QACA;QACA;QACA;MALA,CAfA,EAsBA;QACAJ,+CADA;QAEAE,gBAFA;QAGAC,cAHA;QAIAE,UAJA;QAKAC,UALA;QAMAL,SANA;QAOAM,QAPA;QAQAH;MARA,CAtBA,EAgCA;QACAJ,yCADA;QAEAC,SAFA;QAGAO,gBAHA;QAIA;QACAN,YALA;QAMAE;MANA,CAhCA,EAwCA;QACAJ,mCADA;QAEAC,SAFA;QAGA;QACAC,gBAJA;QAKAE;MALA,CAxCA,EA+CA;QACAJ,gDADA;QAEAE,cAFA;QAGAD,SAHA;QAIAQ,WAJA;QAKAL;MALA,CA/CA,CAZA;MAmEAM,cACA;QACAT,YADA;QAEAD,qDAFA;QAGAW;MAHA,CADA,EAMA;QACAV,eADA;QAEAD,wDAFA;QAGAW;MAHA,CANA,CAnEA;MA+EAC,sBA/EA;MAgFAC,qBAhFA;MAiFAC;IAjFA;EAmFA,CArFA;;EAsFA;IACA;IACA;IACA;IACA;IACA;EACA,CA5FA;;EA6FAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACAC;IACA,CANA;;IAOA;MACA;QACAC;MADA;MAGA;MACA;IACA,CAbA;;IAcAC;MACA;IACA,CAhBA;;IAiBA;MACA;MACA;QACAC,qCADA;QAEAC,+BAFA;QAGAC,mBAHA,CAGA;;MAHA;MAKA;MACA;;MACA;QACAL;;QACA;UACA;UACA;QACA,CAHA,MAGA;UACA;UACA;QACA;MACA;;MACA;MACA;MACA;;MACA;QACA;MACA;;MACA;IACA,CA3CA;;IA4CAM;MACA;QACA;MACA;IACA,CAhDA;;IAiDA;IACA;MACAN;MACA,wCAFA,CAEA;;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAO;MACA,CAFA,MAEA;QACAA;MACA;;MACA;QACAC;UACAC,yCADA;UAEAzB;QAFA;QAIA;MACA;;MACA;MACA;MACA;QACA;UACA0B;UACAC;QACA;MACA,CALA;MAMA;MACA;QACAC;;QACA;UACAA,wBADA,CACA;;UAEAA;QACA;MACA,CAPA;MAQAA;MACAA,qCAtCA,CAsCA;;MACAA;MACAA;MACAA,8CAzCA,CAyCA;;MACA;MACAJ;QACAC,gBADA;QAEAzB;MAFA;MAIA;IACA,CAlGA;;IAmGA;MACA;MACA;QACA;MACA,CAFA;MAGA;QACA0B,8BADA;QAEAG,0BAFA;QAGAC,qFAHA;QAIAC,4FAJA;QAKAC,6BALA;QAMAC,qCANA;QAOAC,uCAPA;QAQAC,+BARA,CASA;QACA;;MAVA;MAYA;MACAX;QACAC,gBADA;QAEAzB;MAFA;MAIA;MACA;IACA,CA3HA;;IA4HA;MACA;MACA;QACA0B,8BADA;QAEAO;MAFA;MAIA;MACAT;QACAC,gBADA;QAEAzB;MAFA;MAIA;MACA;IACA,CAzIA;;IA0IA;MACA;MACA;QACAoC,4CADA;QAEAC;MAFA;MAIA;MACA;MACA;MACA;MACA;IACA,CArJA;;IAsJA;MACA;MACArB;;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;YACAO;YACA;YACA;UACA,CAJA,MAIA;YACAA;YACA;UACA;QACA;MACA,CAXA;;MAYA;QACA;QACA;QAEA;MACA,CALA,MAKA;QACA;QACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACAc,+BADA;QAEAC;MAFA;MAIA;;MACA;QACA;;QACA;UACAC;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAPA,MAOA;QACA;QACAA;MACA;IACA,CA1MA;;IA2MAC;MACA;MACAC;MACA;IACA,CA/MA;;IAgNAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;IACA,CAvNA;;IAwNA;MACA,wCADA,CAEA;MACA;;MACA;QACA;MACA,CAFA;MAGA;QACAC,cADA;QAEAP,4CAFA;QAGAC;MAHA;MAKA;MACAb;QACAC,gBADA;QAEAzB;MAFA;MAIA;MACA;IACA,CA3OA;;IA4OA;MACA;MACA;QACA4C,8BADA;QAEAd,6BAFA;QAGAE;MAHA;MAKA;MACAR;QACAC,gBADA;QAEAzB;MAFA;MAIA;MACA;IACA,CA1PA;;IA4PA;MACA;MACA;QACA6C,yCADA;QAEAb,6BAFA;QAGAc,kCAHA;QAIAC,sBAJA;QAKAd,qCALA;QAMAe;MANA;MAQA;MACAxB;QACAC,gBADA;QAEAzB;MAFA;MAIA;MACA;IACA,CA7QA;;IA8QA;MACA;MACA;QACAiD,uDADA;QAEA;QACAL,8BAHA;QAIAM,sBAJA;QAKAC,YALA;QAMAC;MANA;MAQA;MACA;IACA,CA1RA;;IA2RAC;MACA;IACA,CA7RA;;IA8RAC;MACA;MACA;MACA;IACA;;EAlSA;AA7FA", "names": ["GetPageListMaterialPreDown", "CompletePallet", "OpenPallet", "GetConSelectList", "FirstAddPallet", "DeletePallet", "RemovePallet", "GetConsumlList", "PalletIsFinish_PG", "GetReprintSave", "data", "tableList", "tableId", "header", "selectTabelData", "BatchPallets", "BatchPalletsOption", "<PERSON><PERSON><PERSON>", "materialonly", "Operate", "InQuantity", "PrinterModel", "Printerinputlist", "label", "value", "type", "Required", "id", "value2", "value3", "unit", "filterable", "options", "OperateList", "icon", "myContainerState", "OpenPalletState", "RemarkList", "methods", "ChangeRemark", "handleSelect", "console", "equipmentId", "ClickPrint", "PID", "BatchId", "MCode", "getValue3", "flag", "Message", "message", "containerID", "ContainerName", "addob<PERSON>", "containerIDs", "actualWeight", "TagWeight", "UomID", "MaterialId", "ProID", "BatchID", "proOrderID", "batchID", "eqpmentID", "$", "BatchPalletsChange", "window", "OperateChange", "subIDs", "ContainerId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ProBatchID", "EquipMentID", "ProRequestID", "mId", "EquipmentId", "pageIndex", "pageSize", "handleSelectionChange", "isDateInThePast"], "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sources": ["BatchPallets.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxTitle\" style=\"font-size: 14px\">{{ $t('MaterialPreparationBuild.BatchPallets') }}</div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"inputformbox longwidthinput\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"BatchPallets\" :placeholder=\"$t('MaterialPreparationBuild.BatchPallets')\" @change=\"BatchPalletsChange\">\r\n                        <el-option v-for=\"(it, ind) in BatchPalletsOption\" :key=\"ind\" :label=\"it.ContainerName + '-' + it.ContainerState\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n                <el-button class=\"tablebtn\" size=\"small\" id=\"overBtn\" :disabled=\"myContainerState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getCompletePallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.CompletePallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"OpenPalletState\" style=\"margin-left: 5px; width: 140px\" icon=\"el-icon-success\" @click=\"getOpenPallet()\">\r\n                    {{ this.$t('MaterialPreparationBuild.OpenPallet') }}\r\n                </el-button>\r\n                <div class=\"inputformbox btnselect\" style=\"width: 250px\">\r\n                    <el-select clearable v-model=\"Operate\" :placeholder=\"$t('MaterialPreparationBuild.Operate')\" @change=\"OperateChange\">\r\n                        <el-option v-for=\"item in OperateList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                            <i :class=\"item.icon\"></i>\r\n                            <span style=\"float: right; font-size: 13px\">{{ item.label }}</span>\r\n                        </el-option>\r\n                    </el-select>\r\n                </div>\r\n                <div class=\"inputformbox\" style=\"width: 200px\">\r\n                    <el-checkbox v-model=\"materialonly\" @change=\"getTabelData()\">{{ $t('MaterialPreparationBuild.Currentmaterialonly') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"ClickPrint()\" size=\"small\" style=\"margin-left: 5px; width: 100px; position: absolute; right: 160px\" icon=\"el-icon-printer\">\r\n                    {{ this.$t('MaterialPreparationBuild.ReprintBtn') }}\r\n                </el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    :disabled=\"selectTabelData.length < 0\"\r\n                    size=\"small\"\r\n                    @click=\"GetRemoveBags()\"\r\n                    style=\"margin-left: 5px; width: 140px; position: absolute; right: 10px\"\r\n                    icon=\"el-icon-top\"\r\n                >\r\n                    {{ this.$t('MaterialPreparationBuild.RemoveBags') }}\r\n                </el-button>\r\n            </div>\r\n        </div>\r\n        <el-table :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"500\">\r\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n            <el-table-column\r\n                v-for=\"(item, index) in header\"\r\n                :fixed=\"item.fixed ? item.fixed : false\"\r\n                :key=\"index\"\r\n                :align=\"item.align\"\r\n                :prop=\"item.prop ? item.prop : item.value\"\r\n                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                :width=\"item.width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                    <span v-else-if=\"scope.column.property == 'Material'\">\r\n                        <div>{{ scope.row.MaterialCode }}</div>\r\n                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                        <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                            {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                        </div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                        <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                    </span>\r\n                    <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                    <!-- <span v-else-if=\"scope.column.property == 'operate'\">\r\n                        <el-button size=\"mini\" class=\"operateBtn tablebtn\" @click=\"ClickPrint(scope.row)\" icon=\"el-icon-printer\">{{ $t('PalletList.Reprint') }}</el-button>\r\n                    </span> -->\r\n                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                </template>\r\n                <!-- <template slot-scope=\"scope\"></template> -->\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-dialog :title=\"$t('PalletList.Reprint')\" id=\"Printerdialog\" :visible.sync=\"PrinterModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Printerinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.Required ? item.label + ' *' : item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\"></el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.options\" :key=\"it.ID\" :label=\"it.Code\" :value=\"it.ID\"></el-option>\r\n                        </el-select>\r\n\r\n                        <div v-else-if=\"item.type == 'textArea'\" style=\"display: flex; align-items: center\">\r\n                            <el-input disabled v-model=\"item.value2\"></el-input>\r\n                            <span style=\"margin: 0 5px\">-</span>\r\n                            <el-input onkeyup=\"if(isNaN(value))execCommand('undo')\" onafterpaste=\"if(isNaN(value))execCommand('undo')\" @change=\"getValue3\" v-model=\"item.value3\"></el-input>\r\n                            <span style=\"margin: 0 5px\">=</span>\r\n                            <el-input disabled v-model=\"item.value\"></el-input>\r\n                            <span style=\"margin-left: 10px; width: 18%\">{{ item.unit }}</span>\r\n                        </div>\r\n                        <el-select clearable v-else-if=\"item.type == 'trea'\" v-model=\"item.value\" filterable @change=\"ChangeRemark\">\r\n                            <el-option v-for=\"(it, ind) in RemarkList\" :key=\"ind\" :label=\"it.ItemName\" :value=\"it.ItemName\"></el-option>\r\n                        </el-select>\r\n                        <!-- <el-input v-else-if=\"item.type == 'trea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input> -->\r\n                        <el-input v-else-if=\"item.type == 'textarea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-printer\" @click=\"getReprint()\">\r\n                    {{ $t('PalletList.Reprint') }}\r\n                </el-button>\r\n                <el-button @click=\"PrinterModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { GetPrinit7 } from '@/api/Inventory/common.js';\r\nimport { Message } from 'element-ui';\r\nimport { POBatchPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport $ from 'jquery';\r\nimport {\r\n    GetPageListMaterialPreDown,\r\n    CompletePallet,\r\n    OpenPallet,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    DeletePallet,\r\n    RemovePallet,\r\n    GetConsumlList,\r\n    PalletIsFinish_PG,\r\n    GetReprintSave\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            tableList: [],\r\n            tableId: 'INV_CLZB',\r\n            header: POBatchPalletsColumn,\r\n            selectTabelData: [],\r\n            BatchPallets: '',\r\n            BatchPalletsOption: [],\r\n            detailobj: {},\r\n            materialonly: false,\r\n            Operate: '',\r\n            InQuantity: '',\r\n            PrinterModel: false,\r\n            Printerinputlist: [\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.zts'),\r\n                    value: '',\r\n                    type: 'number',\r\n                    Required: true,\r\n                    id: 'Totalts'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.dqgxh'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'input',\r\n                    // id: 'XHNumber'\r\n                },\r\n                {\r\n                    // label: this.$t('MaterialPreparationBuild.ts'),\r\n                    // value: '',\r\n                    // Required: true,\r\n                    // type: 'number',\r\n                    // id: 'TS'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.jsgs'),\r\n                    type: 'textArea',\r\n                    Required: true,\r\n                    value2: '',\r\n                    value3: '',\r\n                    value: '',\r\n                    unit: '',\r\n                    id: 'WNumber'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CommentsOption'),\r\n                    value: '',\r\n                    filterable: true,\r\n                    //    Required: true,\r\n                    type: 'trea',\r\n                    id: 'RemarkList'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    value: '',\r\n                    //    Required: true,\r\n                    type: 'textarea',\r\n                    id: 'Remark'\r\n                },\r\n                {\r\n                    label: this.$t('MaterialPreparationBuild.xzdyj'),\r\n                    type: 'select',\r\n                    value: '',\r\n                    options: [],\r\n                    id: 'PrintID'\r\n                }\r\n            ],\r\n            OperateList: [\r\n                {\r\n                    value: 'add',\r\n                    label: this.$t('MaterialPreparationBuild.Addpallets'),\r\n                    icon: 'el-icon-plus'\r\n                },\r\n                {\r\n                    value: 'delete',\r\n                    label: this.$t('MaterialPreparationBuild.Deletepallets'),\r\n                    icon: 'el-icon-circle-close'\r\n                }\r\n            ],\r\n            myContainerState: true,\r\n            OpenPalletState: true,\r\n            RemarkList: []\r\n        };\r\n    },\r\n    async mounted() {\r\n        this.room = window.sessionStorage.getItem('room');\r\n        let Remark = await this.$getNewDataDictionary('PalletQuickFields');\r\n        this.RemarkList = Remark;\r\n        this.getSelectList();\r\n        this.getPrintList();\r\n    },\r\n    methods: {\r\n        ChangeRemark(val) {\r\n            this.Printerinputlist[5].value = this.Printerinputlist[5].value + val;\r\n        },\r\n        handleSelect(item) {\r\n            console.log(item);\r\n        },\r\n        async getPrintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res = await GetPrinit7(params);\r\n            this.Printerinputlist[this.Printerinputlist.length - 1].options = res.response;\r\n        },\r\n        ClickPrint() {\r\n            this.getValue1();\r\n        },\r\n        async getValue1() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                PID: this.detailobj.ProductionOrderId,\r\n                BatchId: this.detailobj.BatchId,\r\n                MCode: '7300030001' //水\r\n            };\r\n            let res = await GetConsumlList(params);\r\n            let data = res.response;\r\n            if (data) {\r\n                console.log(data.data);\r\n                if (data.dataCount == 0) {\r\n                    this.Printerinputlist[3].value2 = 0;\r\n                    this.Printerinputlist[3].unit = '';\r\n                } else {\r\n                    this.Printerinputlist[3].value2 = data.data[0].Quantity;\r\n                    this.Printerinputlist[3].unit = data.data[0].UName;\r\n                }\r\n            }\r\n            this.Printerinputlist[4].value = '';\r\n            this.Printerinputlist[5].value = '';\r\n            this.Printerinputlist[6].value = '';\r\n            if (this.Printerinputlist[6].options.length != 0) {\r\n                this.Printerinputlist[6].value = this.Printerinputlist[6].options[0].ID;\r\n            }\r\n            this.PrinterModel = true;\r\n        },\r\n        getValue3(val) {\r\n            if (this.Printerinputlist[3].value2 !== '') {\r\n                this.Printerinputlist[3].value = (Number(this.Printerinputlist[3].value2) - Number(this.Printerinputlist[3].value3)).toFixed(3);\r\n            }\r\n        },\r\n        //打印保存\r\n        async getReprint() {\r\n            console.log(this.BatchPallets);\r\n            this.detailobj = this.$parent.detailobj; //这里的this.detailobj就是工单的数据\r\n            let flag = this.Printerinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.Printerinputlist[3].value3 == '') {\r\n                flag = true;\r\n            } else {\r\n                flag = false;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let ContainerName = '';\r\n            let containerID = '';\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    containerID = item.ID;\r\n                    ContainerName = item.ContainerName;\r\n                }\r\n            });\r\n            let addobj = {};\r\n            this.Printerinputlist.forEach(item => {\r\n                addobj[item.id] = item.value;\r\n                if (item.value2) {\r\n                    addobj.unit = item.unit; //计算公式的单位\r\n\r\n                    addobj.WNumberText = item.value2 + '-' + item.value3 + '=' + item.value;\r\n                }\r\n            });\r\n            addobj.EquipmentId = this.room;\r\n            addobj.ContainerName = ContainerName; // 容器编号\r\n            addobj.ContainerID = containerID;\r\n            addobj.BatchId = this.detailobj.BatchId;\r\n            addobj.PID = this.detailobj.ProductionOrderId; //这里是示例，获取工单ID并且给传参PID赋值\r\n            let res = await GetReprintSave(addobj);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrinterModel = false;\r\n        },\r\n        async getCompletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let containerIDs = this.BatchPalletsOption.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                containerIDs: containerIDs,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                TagWeight: this.detailobj.MQuantityTotal == null ? 0 : Number(this.detailobj.MQuantityTotal),\r\n                UomID: this.detailobj.TUintid,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProID: this.detailobj.ProductionOrderId,\r\n                BatchID: this.detailobj.BatchId\r\n                // ProID:'',\r\n                // BatchID:''\r\n            };\r\n            let res = await CompletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getOpenPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                containerID: this.BatchPallets,\r\n                MaterialId: this.detailobj.MaterialId\r\n            };\r\n            let res = await OpenPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getSelectList() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            this.BatchPalletsOption = res.response;\r\n            this.BatchPallets = this.BatchPalletsOption[0].ID;\r\n            this.getBatchPalletsStatus();\r\n            this.getTabelData();\r\n        },\r\n        async getBatchPalletsStatus() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            console.log(this.detailobj, 666);\r\n            if (this.BatchPallets == '') {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = true;\r\n                return;\r\n            }\r\n\r\n            let flag = false;\r\n            this.BatchPalletsOption.forEach(item => {\r\n                if (item.ID == this.BatchPallets) {\r\n                    if (item.ContainerState == 'complete' || item.ContainerState == '已完成' || item.ContainerState == '拼锅已完成') {\r\n                        flag = true;\r\n                        this.myContainerState = true;\r\n                        return false;\r\n                    } else {\r\n                        flag = false;\r\n                        this.myContainerState = false;\r\n                    }\r\n                }\r\n            });\r\n            if (flag) {\r\n                this.myContainerState = true;\r\n                this.OpenPalletState = false;\r\n\r\n                return false;\r\n            } else {\r\n                this.myContainerState = false;\r\n                this.OpenPalletState = true;\r\n            }\r\n            if (this.detailobj.MQuantity >= this.detailobj.MinPvalue && this.detailobj.MQuantity <= this.detailobj.MaxPvalue) {\r\n                this.myContainerState = false;\r\n            } else {\r\n                this.myContainerState = true;\r\n            }\r\n            let params = {\r\n                batchID: this.detailobj.BatchId,\r\n                eqpmentID: this.room\r\n            };\r\n            let res = await PalletIsFinish_PG(params);\r\n            if (res.msg != '失败') {\r\n                this.myContainerState = false;\r\n                if (this.myContainerState == false) {\r\n                    $('#overBtn').addClass('myfadeIn');\r\n                } else {\r\n                    $('#overBtn').removeClass('myfadeIn');\r\n                }\r\n            } else {\r\n                this.myContainerState = true;\r\n                $('#overBtn').removeClass('myfadeIn');\r\n            }\r\n        },\r\n        BatchPalletsChange() {\r\n            this.getBatchPalletsStatus();\r\n            window.sessionStorage.setItem('BatchPallets', this.BatchPallets);\r\n            this.getTabelData();\r\n        },\r\n        OperateChange(val) {\r\n            if (val == 'add') {\r\n                this.AddNewPallet();\r\n            } else if (val == 'delete') {\r\n                this.getDeletePallet();\r\n            }\r\n            this.Operate = '';\r\n        },\r\n        async GetRemoveBags() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            // alert(this.detailobj);\r\n            // console.log(this.detailobj);\r\n            let subIDs = this.selectTabelData.map(item => {\r\n                return item.SubId;\r\n            });\r\n            let params = {\r\n                subIDs: subIDs,\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await RemovePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getDeletePallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                ContainerId: this.BatchPallets,\r\n                actualWeight: this.InQuantity,\r\n                UomID: this.detailobj.TUintid\r\n            };\r\n            let res = await DeletePallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n\r\n        async AddNewPallet() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$parent.refresh();\r\n            this.$parent.EmptySscc();\r\n        },\r\n        async getTabelData() {\r\n            this.detailobj = this.$parent.detailobj;\r\n            let params = {\r\n                mId: this.materialonly ? this.detailobj.MaterialId : '',\r\n                // MaterialId: this.detailobj.MaterialId,\r\n                ContainerId: this.BatchPallets,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListMaterialPreDown(params);\r\n            this.tableList = res.response.data;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.buildpalletsStart {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin: 10px 0;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 20%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .inputformbox {\r\n        width: 18vh;\r\n    }\r\n}\r\n#Printerdialog {\r\n    .colInputLabel {\r\n        margin-top: 10px;\r\n        font-weight: 600;\r\n    }\r\n    .el-select {\r\n        margin-top: 10px;\r\n        width: 100% !important;\r\n    }\r\n    .el-autocomplete {\r\n        width: 100% !important;\r\n    }\r\n}\r\n.buildpalletsStart .longwidthinput .el-select {\r\n    width: 100%;\r\n}\r\n\r\n.buildpalletsStart .btnselect .el-input__inner {\r\n    // background: #3dcd58;\r\n    ::placeholder {\r\n        color: #fff;\r\n    }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\MaterialSplit.vue", "mtime": 1749634431430}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MaterialSplit.vue"], "names": [], "mappings": ";AAgEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MaterialSplit.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\n    <div class=\"usemystyle MaterialSplit\">\n        <div class=\"tabinputbox\">\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    @change=\"getRowBySSCC\" \n                    ref=\"autoFocus\" \n                    :placeholder=\"$t('Consume.SSCC')\" \n                    v-model=\"sscc\"\n                    :disabled=\"!splitVisible\">\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <el-input \n                    size=\"mini\" \n                    :placeholder=\"$t('MaterialPreparationBuild.SplitQuantity')\" \n                    v-model=\"splitQuantity\"\n                    :disabled=\"!splitVisible || !ssccFlag\">\n                    <template slot=\"append\">{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}</template>\n                </el-input>\n            </div>\n            <div class=\"tabinputsinglebox\">\n                <div class=\"tabbtnsinglebox\">\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        :disabled=\"!canSplit\" \n                        size=\"small\" \n                        icon=\"el-icon-scissors\" \n                        class=\"tablebtn\" \n                        @click=\"performSplit()\">\n                        {{ this.$t('MaterialPreparationBuild.Split') }}\n                    </el-button>\n                    <el-button \n                        style=\"margin-left: 5px\" \n                        size=\"small\" \n                        icon=\"el-icon-view\" \n                        @click=\"toggleSplitVisibility()\">\n                        {{ splitVisible ? $t('MaterialPreparationBuild.HideSplit') : $t('MaterialPreparationBuild.ShowSplit') }}\n                    </el-button>\n                </div>\n            </div>\n        </div>\n        \n        <!-- 分包信息显示 -->\n        <div v-if=\"splitVisible && selectedMaterial\" class=\"split-info-box\">\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.SelectedMaterial') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.MaterialCode }} - {{ selectedMaterial.MaterialName }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.AvailableQuantity') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.InQuantity }}{{ selectedMaterial.MaterialUnit1 }}</span>\n            </div>\n            <div class=\"info-item\">\n                <span class=\"info-label\">{{ $t('MaterialPreparationBuild.BatchNumber') }}:</span>\n                <span class=\"info-value\">{{ selectedMaterial.LBatch }}</span>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport { splitMaterial } from '@/api/materialManagement/lineLibraryManagement.js';\nimport { Message } from 'element-ui';\n\nexport default {\n    data() {\n        return {\n            sscc: '',\n            splitQuantity: '',\n            ssccFlag: false,\n            splitVisible: false,\n            selectedMaterial: null,\n            detailobj: {}\n        };\n    },\n    computed: {\n        canSplit() {\n            return this.splitVisible && \n                   this.ssccFlag && \n                   this.sscc !== '' && \n                   this.splitQuantity !== '' && \n                   Number(this.splitQuantity) > 0 &&\n                   this.selectedMaterial &&\n                   Number(this.splitQuantity) < Number(this.selectedMaterial.InQuantity);\n        }\n    },\n    mounted() {\n        this.detailobj = JSON.parse(this.$route.query.query);\n    },\n    methods: {\n        toggleSplitVisibility() {\n            this.splitVisible = !this.splitVisible;\n            if (!this.splitVisible) {\n                this.resetSplitForm();\n            }\n        },\n        \n        resetSplitForm() {\n            this.sscc = '';\n            this.splitQuantity = '';\n            this.ssccFlag = false;\n            this.selectedMaterial = null;\n        },\n        \n        getRowBySSCC() {\n            this.$emit('getRowBySscc', this.sscc);\n        },\n        \n        // 从父组件接收选中的物料信息\n        setSelectedMaterial(material) {\n            this.selectedMaterial = material;\n            this.ssccFlag = true;\n        },\n        \n        async performSplit() {\n            if (!this.canSplit) {\n                Message({\n                    message: this.$t('MaterialPreparationBuild.SplitConditionNotMet'),\n                    type: 'warning'\n                });\n                return;\n            }\n            \n            try {\n                const splitData = {\n                    materialId: this.selectedMaterial.MaterialId,\n                    sscc: this.sscc,\n                    originalQuantity: this.selectedMaterial.InQuantity,\n                    splitQuantity: Number(this.splitQuantity),\n                    batchNumber: this.selectedMaterial.LBatch,\n                    equipmentId: window.sessionStorage.getItem('room'),\n                    unitId: this.selectedMaterial.UnitId\n                };\n                \n                const result = await splitMaterial(splitData);\n                \n                Message({\n                    message: result.msg || this.$t('MaterialPreparationBuild.SplitSuccess'),\n                    type: 'success'\n                });\n                \n                // 重置表单\n                this.resetSplitForm();\n                \n                // 通知父组件刷新数据\n                this.$emit('getRefresh');\n                \n            } catch (error) {\n                Message({\n                    message: error.message || this.$t('MaterialPreparationBuild.SplitFailed'),\n                    type: 'error'\n                });\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.MaterialSplit {\n    .split-info-box {\n        margin-top: 10px;\n        padding: 10px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border: 1px solid #e4e7ed;\n        \n        .info-item {\n            display: flex;\n            margin-bottom: 5px;\n            \n            .info-label {\n                font-weight: 600;\n                color: #606266;\n                min-width: 120px;\n            }\n            \n            .info-value {\n                color: #303133;\n            }\n        }\n    }\n}\n</style>\n"]}]}